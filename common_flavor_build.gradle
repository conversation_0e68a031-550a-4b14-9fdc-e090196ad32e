apply from:"../../common_build.gradle"

android {
    flavorDimensions "B", "P", "region", "apilevel"
    productFlavors {
        //apk名称需要重命名为：“OPPO”
        oppo {
            dimension "B"
        }
        //apk名称需要重命名为：“OnePlus”
        oneplus {
            dimension "B"
        }
        domestic {
            dimension "region"
        }
        export {
            dimension "region"
            //默认情况，只编译核心语言，但是服务器编译，会动态修改，编译外销需要的所有语言,如没有配置，会是全语言
            if (prop_disableSubPackage.toBoolean()) {
                println("app disable resource subpacakge")
            } else {
                if (!prop_exp_resConfig.toString().isEmpty()) {
                    resConfigs prop_exp_resConfig
                } else {
                    println("subpacakge config is empty, no subpackage")
                }
            }
        }
        //apk名称需要重命名为：“GDPR”
        gdpr {
            dimension "region"
            //默认情况，只编译核心语言，但是服务器编译，会动态修改，编译外销需要的所有语言,如没有配置，会是全语言
            if (prop_disableSubPackage.toBoolean()) {
                println("app disable resource subpacakge")
            } else {
                if (!prop_exp_resConfig.toString().isEmpty()) {
                    resConfigs prop_exp_resConfig
                } else {
                    println("subpacakge config is empty, no subpackage")
                }
            }
        }
        aall {
            dimension "apilevel"
        }
        pall {
            dimension "P"
        }
    }

    configurations {
        oneplusPallDomesticAallImplementation
        oneplusPallExportAallImplementation
        oneplusPallGdprAallImplementation
    }

    sourceSets {
        main {
            manifest.srcFile 'src/main/AndroidManifest.xml'
        }
        domestic {
            java.srcDirs += ['domestic/src/main/java']
            res.srcDirs += ['domestic/src/main/res', '../../res-strings-domestic']
            manifest.srcFile 'domestic/src/main/AndroidManifest.xml'
        }

        export {
            java.srcDirs += ['export/src/main/java']
            res.srcDirs += ['export/src/main/res', '../../res-strings-export']
            manifest.srcFile 'export/src/main/AndroidManifest.xml'
        }

        gdpr {
            java.srcDirs += ['export/src/main/java']
            res.srcDirs += ['export/src/main/res', '../../res-strings-export']
            manifest.srcFile 'export/src/main/AndroidManifest.xml'
        }
    }
}
