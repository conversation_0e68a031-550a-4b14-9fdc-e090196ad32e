<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/coui_color_label_on_color">

    <!-- 工具栏 -->
    <com.coui.appcompat.toolbar.COUIToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp52"
        android:background="@color/coui_color_container4"
        app:navigationIcon="@drawable/ic_arrow_back"
        app:title="动画参数编辑"
        app:titleTextColor="@color/coui_color_label_primary" />

    <!-- 滚动容器 -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:fillViewport="true">

        <LinearLayout
            android:id="@+id/scroll_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="@dimen/dp16" />

    </ScrollView>

    <!-- 底部确认按钮 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="@dimen/dp16"
        android:gravity="center"
        android:background="@color/coui_color_container4">

        <Button
            android:id="@+id/confirm_button"
            android:layout_width="200dp"
            android:layout_height="48dp"
            android:text="确认"
            android:textSize="16sp"
            android:textColor="@color/coui_color_label_on_color"
            android:background="@drawable/button_primary_background"
            android:layout_gravity="center" />

    </LinearLayout>

</LinearLayout>
