package com.soundrecorder.record.views

import android.view.View
import android.view.ViewGroup.MarginLayoutParams
import android.widget.LinearLayout
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.ConstraintSet
import com.coui.appcompat.animation.dynamicanimation.COUIDynamicAnimation
import com.coui.appcompat.animation.dynamicanimation.COUISpringAnimation
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.DensityUtil.px2dp
import com.soundrecorder.record.RecorderActivity
import com.soundrecorder.record.views.wave.RecorderWaveRecyclerView
import com.soundrecorder.wavemark.wave.view.WaveItemView
import com.soundrecorder.wavemark.wave.view.WaveViewGradientLayout
import kotlin.math.floor

class WaveAnimationHelper(val activity: RecorderActivity, var isRealTimeSwitch: Boolean = false) {

    companion object {
        const val TAG = "WaveAnimationHelper"
        const val ANIMATION_TYPE_HEIGHT = 1
        const val ANIMATION_TYPE_MARGIN_TOP = 2
        const val ANIMATION_TYPE_SIZE = 3
        const val ANIMATION_TYPE_ALPHA = 4
    }

    var realTimeView: LinearLayout? = null
    var waveGradientView: WaveViewGradientLayout? = null
    var rulerView: RecorderWaveRecyclerView? = null
    var recorderTop: LinearLayout? = null
    var timerView: TextView? = null
    var rootView: ConstraintLayout? = null
    var translationBtn: TextView? = null
    val animationList: ArrayList<AnimationItem> = ArrayList()

    init {
        realTimeView = activity.findViewById(com.soundrecorder.record.R.id.real_time_view)
        waveGradientView = activity.findViewById(com.soundrecorder.record.R.id.wave_gradient_view)
        rulerView = activity.findViewById(com.soundrecorder.record.R.id.ruler_view)
        recorderTop = activity.findViewById(com.soundrecorder.record.R.id.recorder_top)
        timerView = activity.findViewById(com.soundrecorder.record.R.id.timerView)
        rootView = activity.findViewById(com.soundrecorder.record.R.id.root_view)
        translationBtn = activity.findViewById(com.soundrecorder.record.R.id.transcription_tv)
        initViewAndAnimation()
    }

    fun startAnimation() {
        if (!animationIsRunning()) {
            animationList.forEach { animationItem ->
                animationItem.animation.apply {
                    spring.setFinalPosition(animationItem.startValue)
                    start()
                }
                val startValue = animationItem.startValue
                animationItem.startValue = animationItem.finalPosition
                animationItem.finalPosition = startValue
            }
        } else {
            animationList.forEach { animationItem ->
                animationItem.view.visibility = View.VISIBLE
                animationItem.animation.start()
            }
        }
    }

    private fun addAnimationEndListener(): Boolean {
        return animationIsRunning()
    }

    private fun animationIsRunning(): Boolean {
        animationList.forEach { animationItem ->
            if (animationItem.animation.isRunning) {
                return false
            }
        }
        return true
    }

    private fun initViewAndAnimation() {
        val timerViewSizeSmall = activity.baseContext.px2dp(activity.resources.getDimension(com.soundrecorder.common.R.dimen.sp37).toInt())
        val timerViewSizeLarge = activity.baseContext.px2dp(activity.resources.getDimension(com.soundrecorder.common.R.dimen.sp48).toInt())

        val recorderTopSmall = activity.resources.getDimension(com.soundrecorder.common.R.dimen.dp8)
        val recorderTopLarge = activity.resources.getDimension(com.soundrecorder.common.R.dimen.dp40)

        val waveHeightLarge = activity.resources.displayMetrics.heightPixels * 0.4f
        val waveHeightSmall = activity.resources.displayMetrics.heightPixels * 0.15f

        val waveMarginTopLarge = activity.resources.getDimension(com.soundrecorder.record.R.dimen.wave_margin_top)
        val waveMarginTopSmall = activity.resources.getDimension(com.soundrecorder.common.R.dimen.dp20)

        animationList.clear()
        waveGradientView?.visibility = View.VISIBLE
        if (isRealTimeSwitch) {
            realTimeView?.let {
                it.visibility = View.VISIBLE
                it.alpha = 1f
                createAnimation(it, COUISpringAnimation.ALPHA, 1f, 0f, 0.2f, 0.2f)
            }

            timerView?.let {
                timerView?.textSize = floor(timerViewSizeSmall)
                createConstrainAnimation(it, timerViewSizeSmall, timerViewSizeLarge, 0.2f, 0.2f, ANIMATION_TYPE_SIZE)
            }

            recorderTop?.let {
                val recorderTopParams = it.layoutParams as? MarginLayoutParams
                recorderTopParams?.topMargin = floor(recorderTopSmall).toInt()
                createConstrainAnimation(it, recorderTopSmall, recorderTopLarge, 0.2f, 0.2f, ANIMATION_TYPE_MARGIN_TOP)
            }

            waveGradientView?.let {
                val waveLayoutParams = waveGradientView?.layoutParams as? MarginLayoutParams
                waveLayoutParams?.height = floor(waveHeightSmall).toInt()
                waveLayoutParams?.topMargin = floor(waveMarginTopSmall).toInt()
                waveGradientView?.layoutParams = waveLayoutParams
                createConstrainAnimation(it, waveHeightSmall, waveHeightLarge, 0.2f, 0.2f, ANIMATION_TYPE_HEIGHT)
                createConstrainAnimation(it, waveMarginTopSmall, waveMarginTopLarge, 0.2f, 0.2f, ANIMATION_TYPE_MARGIN_TOP)
            }

            rulerView?.let {
                WaveItemView.updateTimeLinesAlpha(0)
                createConstrainAnimation(it, 0f, 1f, 0.8f, 0.8f, ANIMATION_TYPE_ALPHA)
            }

            translationBtn?.let {
                it.alpha = 0f
                createAnimation(it, COUISpringAnimation.ALPHA, 0f, 1f, 0.3f, 0.3f)
            }

        } else {
            realTimeView?.let {
                it.visibility = View.GONE
                it.alpha = 0f
                createAnimation(it, COUISpringAnimation.ALPHA, 0f, 1f, 0.2f, 0.2f)
            }

            timerView?.let {
                timerView?.textSize = floor(timerViewSizeLarge)
                createConstrainAnimation(it, timerViewSizeLarge, timerViewSizeSmall, 0.2f, 0.2f, ANIMATION_TYPE_SIZE)
            }

            recorderTop?.let {
                val recorderTopParams = it.layoutParams as? MarginLayoutParams
                recorderTopParams?.topMargin = floor(recorderTopLarge).toInt()
                createConstrainAnimation(it, recorderTopLarge, recorderTopSmall, 0.2f, 0.2f, ANIMATION_TYPE_MARGIN_TOP)
            }

            waveGradientView?.let {
                val waveLayoutParams = waveGradientView?.layoutParams as? MarginLayoutParams
                waveLayoutParams?.height = floor(waveHeightLarge).toInt()
                waveLayoutParams?.topMargin = floor(waveMarginTopLarge).toInt()
                waveGradientView?.layoutParams = waveLayoutParams
                createConstrainAnimation(it, waveHeightLarge, waveHeightSmall, 0.2f, 0.2f, ANIMATION_TYPE_HEIGHT)
                createConstrainAnimation(it, waveMarginTopLarge, waveMarginTopSmall, 0.2f, 0.2f, ANIMATION_TYPE_MARGIN_TOP)
            }

            rulerView?.let {
                WaveItemView.updateTimeLinesAlpha(1)
                createConstrainAnimation(it, 1f, 0f, 0.8f, 0.8f, ANIMATION_TYPE_ALPHA)
            }

            translationBtn?.let {
                it.alpha = 1f
                createAnimation(it, COUISpringAnimation.ALPHA, 1f, 0f, 0.3f, 0.3f)
            }
        }
    }

    private fun createConstrainAnimation(
        view: View,
        startValue: Float,
        finalPosition: Float,
        response: Float,
        bounds: Float,
        animationType: Int
    ) {
        // 创建一个虚拟视图用于动画值变化
        val dummyView = View(activity)
        val animation = createAnimation(
            dummyView,
            COUISpringAnimation.ALPHA,
            startValue,
            finalPosition,
            response,
            bounds
        )
        // 添加动画更新监听器
        animation.addUpdateListener { _, value, _ ->
            // 在动画过程中更新约束
            val constraintSet = ConstraintSet()
            constraintSet.clone(rootView)
            when(animationType) {
                ANIMATION_TYPE_HEIGHT -> constraintSet.constrainHeight(view.id, value.toInt())
                ANIMATION_TYPE_MARGIN_TOP -> constraintSet.setMargin(view.id, ConstraintSet.TOP, value.toInt())
                ANIMATION_TYPE_SIZE -> {
                    if (view is TextView) {
                        view.textSize = value
                    }
                }
                ANIMATION_TYPE_ALPHA -> {
                    DebugUtil.d("ALPHAIS", "values is $value")
                    WaveItemView.updateTimeLinesAlpha(value.toInt())
                }
                else -> DebugUtil.d(TAG, "animationType error, animation invalid")
            }
            constraintSet.applyTo(rootView)
        }

        animation.addEndListener { couiDynamicAnimation, b, fl, fl2 ->
            val animationResult = addAnimationEndListener()
            if (animationResult) {
                initViewAndAnimation()
            }
        }
    }

    private fun createAnimation(
        view: View,
        type: COUIDynamicAnimation.ViewProperty,
        startValue: Float,
        finalPosition: Float,
        response: Float,
        bounds: Float
    ): COUISpringAnimation {
        val animation = COUISpringAnimation(view, type, finalPosition).apply {
            spring.setFinalPosition(finalPosition)
            spring.setResponse(response)
            spring.setBounce(bounds)
        }
        animation.setStartValue(startValue)
        animationList.add(AnimationItem(view, animation, startValue, finalPosition))
        return animation
    }

    data class AnimationItem(val view: View, val animation: COUISpringAnimation, var startValue: Float, var finalPosition: Float)
}