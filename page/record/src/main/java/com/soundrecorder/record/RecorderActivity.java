/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: RecorderActivity.java
 Description:
 Version: 1.0
 Date: 2009-12-15
 Author: zhanghr
 -----------Revision History-----------
 <author> <date> <version> <desc>
 zhanghr 2009-12-15 create
 */

// OPLUS Java File Skip Rule:MethodLength,MethodComplexity,FileLength,LineLength,VariableInitialization,NumberIntUse,IllegalCatch
package com.soundrecorder.record;

import static android.view.View.GONE;
import static android.view.View.INVISIBLE;
import static android.view.View.VISIBLE;
import static com.soundrecorder.base.splitwindow.FoldingWindowObserver.SCREEN_HORIZONTAL_HOVER;
import static com.soundrecorder.common.constant.RecorderConstant.RECORDER_AUDIO_FORMAT_AMR_NB;
import static com.soundrecorder.modulerouter.privacyPolicy.PrivacyPolicyConstant.TYPE_PERMISSION_SMART_SHORTHAND;
import static com.soundrecorder.modulerouter.recorder.RecordInterfaceKt.FROM_SWITCH_RECORD_STATUS_NORMAL;
import static com.soundrecorder.modulerouter.recorder.RecorderViewModelConstantKt.HALT_ON;
import static com.soundrecorder.modulerouter.recorder.RecorderViewModelConstantKt.INIT;
import static com.soundrecorder.modulerouter.recorder.RecorderViewModelConstantKt.PAUSED;
import static com.soundrecorder.modulerouter.recorder.RecorderViewModelConstantKt.RECORDING;
import static com.soundrecorder.wavemark.wave.WaveViewUtil.WaveType.LARGE;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.AnimatorSet;
import android.annotation.SuppressLint;
import android.app.Activity;
import android.app.RecoverableSecurityException;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.IntentSender;
import android.content.res.Configuration;
import android.graphics.Rect;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.provider.MediaStore.Audio.Media;
import android.text.SpannableStringBuilder;
import android.text.TextUtils;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.view.View.OnCreateContextMenuListener;
import android.view.ViewGroup;
import android.view.Window;
import android.view.accessibility.AccessibilityEvent;
import android.widget.Button;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.Space;
import android.widget.TextView;

import androidx.annotation.LayoutRes;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.RequiresApi;
import androidx.appcompat.app.ActionBar;
import androidx.appcompat.app.AlertDialog;
import androidx.asynclayoutinflater.view.AsyncLayoutInflater;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.graphics.Insets;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowCompat;
import androidx.core.view.WindowInsetsCompat;
import androidx.core.view.WindowInsetsControllerCompat;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.Observer;
import androidx.lifecycle.ViewModelProvider;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.recyclerview.widget.COUIRecyclerView;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.coui.appcompat.contextutil.COUIContextUtil;
import com.coui.appcompat.dialog.COUIAlertDialogBuilder;
import com.coui.appcompat.toolbar.COUIToolbar;
import com.coui.appcompat.tooltips.COUIImageBubbleStyleImpl;
import com.coui.appcompat.tooltips.COUIToolTips;
import com.oplus.anim.EffectiveAnimationView;
import com.photoviewer.ui.PhotoViewerActivity;
import com.soundrecorder.base.BaseApplication;
import com.soundrecorder.base.ext.ExtKt;
import com.soundrecorder.base.ext.IntentExt;
import com.soundrecorder.base.splitwindow.FoldingWindowObserver;
import com.soundrecorder.base.utils.ActivityRunnable;
import com.soundrecorder.base.utils.BaseUtil;
import com.soundrecorder.base.utils.ClickUtils;
import com.soundrecorder.base.utils.DebugUtil;
import com.soundrecorder.base.utils.FeatureOption;
import com.soundrecorder.base.utils.LanguageUtil;
import com.soundrecorder.base.utils.NetworkUtils;
import com.soundrecorder.base.utils.NightModeUtil;
import com.soundrecorder.base.utils.NumberConstant;
import com.soundrecorder.base.utils.OSDKCompatUtils;
import com.soundrecorder.base.utils.PrefUtil;
import com.soundrecorder.base.utils.ScreenUtil;
import com.soundrecorder.base.utils.StatusBarUtil;
import com.soundrecorder.base.utils.TimeUtils;
import com.soundrecorder.base.utils.ToastManager;
import com.soundrecorder.common.buryingpoint.BuryingPoint;
import com.soundrecorder.common.buryingpoint.BuryingPointUtil;
import com.soundrecorder.common.buryingpoint.RecorderUserAction;
import com.soundrecorder.common.buryingpoint.SummaryStaticUtil;
import com.soundrecorder.common.constant.Constants;
import com.soundrecorder.common.constant.OplusCompactConstant;
import com.soundrecorder.common.databean.ConvertContentItem;
import com.soundrecorder.common.databean.MarkDataSource;
import com.soundrecorder.common.databean.MarkMetaData;
import com.soundrecorder.common.databean.markdata.MarkDataBean;
import com.soundrecorder.common.db.MediaDBUtils;
import com.soundrecorder.common.dialog.EditMarkDialog;
import com.soundrecorder.common.dialog.LoadingDialog;
import com.soundrecorder.common.permission.PermissionActivity;
import com.soundrecorder.common.permission.PermissionUtils;
import com.soundrecorder.common.realtimeasr.IRealtimeSubtitleCache;
import com.soundrecorder.common.realtimeasr.OnRealtimeListener;
import com.soundrecorder.common.realtimeasr.RealTimeAsrStatus;
import com.soundrecorder.common.task.ActivityTaskUtils;
import com.soundrecorder.common.utils.FoldStateLiveData;
import com.soundrecorder.common.utils.HomeWatchUtils;
import com.soundrecorder.common.utils.RecordModeUtil;
import com.soundrecorder.common.utils.SafeAsyncLayoutInflater;
import com.soundrecorder.common.utils.TipUtil;
import com.soundrecorder.common.utils.ViewExtKt;
import com.soundrecorder.common.utils.ViewUtils;
import com.soundrecorder.common.utils.sound.DeleteSoundEffectManager;
import com.soundrecorder.common.utils.taskbar.TaskBarUtil;
import com.soundrecorder.common.view.ShadowViewDraw;
import com.soundrecorder.common.widget.AnimatedCircleButton;
import com.soundrecorder.common.widget.TransitionUtils;
import com.soundrecorder.modulerouter.convertService.listener.RecorderControllerListener;
import com.soundrecorder.modulerouter.waveMark.IIPictureMarkListener;
import com.soundrecorder.modulerouter.waveMark.IPictureMarkDelegate;
import com.soundrecorder.modulerouter.miniapp.MiniAppConstant;
import com.soundrecorder.modulerouter.miniapp.MiniAppInterface;
import com.soundrecorder.modulerouter.privacyPolicy.IPrivacyPolicyDelegate;
import com.soundrecorder.modulerouter.recorder.MarkAction;
import com.soundrecorder.modulerouter.recorder.RecorderDataConstant;
import com.soundrecorder.modulerouter.recorder.RecorderServiceInterface;
import com.soundrecorder.modulerouter.recorder.SaveFileState;
import com.soundrecorder.modulerouter.recorder.WaveState;
import com.soundrecorder.modulerouter.smartname.IPluginDownloadCallback;
import com.soundrecorder.modulerouter.smartname.IUnifiedSummaryCallBack;
import com.soundrecorder.modulerouter.smartname.SmartNameAction;
import com.soundrecorder.modulerouter.translate.AIAsrManagerAction;
import com.soundrecorder.modulerouter.translate.IAsrPluginCallBack;
import com.soundrecorder.modulerouter.utils.KoinInterfaceHelper;
import com.soundrecorder.privacypolicy.PrivacyPolicyApiExtKt;
import com.soundrecorder.record.picturemark.PopPicture;
import com.soundrecorder.record.picturemark.PopViewController;
import com.soundrecorder.record.picturemark.PopViewDataCallback;
import com.soundrecorder.record.picturemark.PopViewLoadingActivity;
import com.soundrecorder.record.picturemark.RecorderPictureMarkRecommendHelper;
import com.soundrecorder.record.picturemark.view.PictureSelectActivity;
import com.soundrecorder.record.subtitle.SubtitleMarkInsertHelper;
import com.soundrecorder.record.views.LanguagePopupMenuManager;
import com.soundrecorder.record.views.RecorderAnimatedCircleButton;
import com.soundrecorder.record.views.WaveAnimationHelper;
import com.soundrecorder.record.views.dialog.SaveFileAlertDialog;
import com.soundrecorder.record.views.dialog.VerticalButtonDialogCallback;
import com.soundrecorder.record.views.wave.RecorderWaveRecyclerView;
import com.soundrecorder.recorderservice.manager.RecorderViewModel;
import com.soundrecorder.recorderservice.manager.statusbar.RecordStatusBarUpdater;
import com.soundrecorder.wavemark.mark.MarkListAdapter;
import com.soundrecorder.wavemark.wave.WaveViewUtil;
import com.soundrecorder.wavemark.wave.view.MaxAmplitudeSource;
import com.soundrecorder.wavemark.wave.view.WaveViewGradientLayout;

import org.jetbrains.annotations.NotNull;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.function.Consumer;

import kotlin.Unit;
import kotlin.collections.CollectionsKt;
import kotlin.jvm.functions.Function0;
import kotlin.jvm.functions.Function1;

public class RecorderActivity extends PermissionActivity
        implements OnCreateContextMenuListener, VerticalButtonDialogCallback,
        PopViewDataCallback, RecorderControllerListener, View.OnClickListener,
        IIPictureMarkListener<MarkMetaData, MarkDataBean>, OnRealtimeListener {

    public static final int RECORDER_SET_FORMAT_AMR = 1;
    public static final float TOUCH_LIMIT = 100f;
    public static final int MARK_DISABLE_TIME = 1000;
    private static final String TAG = "RecorderActivity";
    /*用于识别,统计页面 勿改*/
    private static final String FUNCTION_NAME = "Recorder";
    private static final String UPDATE_WAVE_FROM_WINDOW_FOLD = "foldWindow";

    private static final String STATUS_BAR_KEY = "status_bar";
    private static final String RECORD_ENHANCE_STATUS_KEY = "record_enhance_status_key";
    private static final long DELAY_350 = 350;
    private static final long DELAY_850 = 850;
    private static final int MIN_SIZE_LIMIT = 2000;
    private static final int QUICK_CLICK_INTERVAL = 700;
    private static final long DELAY_TIME_DO_SUMMARY = 750L;
    /**
     * delays to show save-record-file-dialog
     */
    private static final int DELAYS_SHOW_SAVE_RECORD_FILE_DIALOG = 30;

    private final Handler mTimerHandler = new Handler(Looper.getMainLooper());
    private final RecorderServiceInterface mRecorderViewModelApi = KoinInterfaceHelper.INSTANCE.getRecorderViewModelApi();
    private final MiniAppInterface mMiniAppApi = KoinInterfaceHelper.INSTANCE.getMiniAppApi();
    private final SmartNameAction mSmartNameAction = KoinInterfaceHelper.INSTANCE.getSmartNameAction();
    private final AIAsrManagerAction mAiAsrManagerAction = KoinInterfaceHelper.INSTANCE.getAiAsrManagerAction();
    private ConstraintLayout mCaptionsGradientView;
    private COUIRecyclerView mCaptionsRecyclerView;
    private LinearLayout mCaptionsLoadingView;
    private TextView mCaptionsTextView;
    private EffectiveAnimationView mCaptionsAnimationView;
    private SubtitleDataInsertHelper mSubtitleDataInsertHelper;

    PopViewController mPopViewController;


    private final Function0<Unit> mPermissionGrantedListener = () -> {
        DebugUtil.i(TAG, "onPermissionGranted");
        if (PermissionUtils.hasReadAudioPermission()
                && PermissionUtils.hasRecordAudioPermission()) {
            if (!mRecorderViewModelApi.hasInitRecorderService()
                    && (mRecorderViewModelApi.getCurrentStatus() == INIT)
                    && !RecorderViewModel.getInstance().isRecorderFinish()) {
                DebugUtil.i(TAG, "onPermissionGranted startPlayNow");
                startPlayNow();
            }
        }
        return Unit.INSTANCE;
    };
    private boolean mPressHomeKeyFlag = false;
    private boolean mPressRecentAppFlag = false;

    private RecorderWaveRecyclerView mWaveRecyclerView;
    private WaveViewGradientLayout mWaveGradientView;
    // 小波形图相关控件
    private TextView mTimerTextView;

    private TextView mTranscriptionTv;
    private LinearLayout mTranscriptionBox;
    private LinearLayout mRecordTop;
    private ImageView mSharedView;
    private ViewGroup mMiddleControl;
    private ShadowViewDraw mMiddleControlShadow;
    private RecorderAnimatedCircleButton mRedCircleView;
    private ViewGroup mRootView;
    private View mNavigationViewOnTaskBar;
    private AnimatedCircleButton mRecorderSave;
    private RecorderAnimatedCircleButton mMarkView;
    private MarkListAdapter mMarkListAdapter;
    private SubtitleMarkInsertHelper mSubtitleMarkInsertHelper;
    private boolean mIsNeedResult = false;
    private Context mContext;
    private WeakReference<RecorderActivity> mWeakRecorderActivity = null;
    private LanguagePopupMenuManager mLanguagePopupMenuManager;

    /*录制模式：标准/会议/采访*/
    private int mTypePosition = 0;
    private COUIToolbar mToolbar;
    private LinearLayout mRealTimeView;
    private ImageView mRealTimeViewDown;
    private TextView mSelectLanguageTv;
    private boolean mFileOverLimit = false;
    private boolean mPressedSave = false;
    private ExecutorService mExecutor = Executors.newSingleThreadExecutor();
    private int mRecorderPosition = -1;
    private boolean mIsFromCallUi = false;
    private boolean mNeedOnlyBindService = false;
    private boolean mNotEnterAnimation = false;
    private LoadingDialog mLoadingDialog = null;
    private SaveFileAlertDialog mVerticalButtonDialog = null;
    private AlertDialog mCancelDialog = null;
    private DialogInterface.OnClickListener mCancelConfirmListener = null;
    private Rect mLastAppBounds;

    private boolean mLastUnfold = false;
    private boolean mIsDoEnterWaveAnimation = false;
    private RecorderPictureMarkRecommendHelper mPictureMarkRecommendHelper;
    private int mFoldWindowType = FoldingWindowObserver.SCREEN_VERTICAL_EXPAND;
    private RecordToastSnackBarControl mRecordToastSnackBarControl;
    private MyBroadcastReceiver mReceiver = new MyBroadcastReceiver(RecorderActivity.this);
    private RecordViewAnimationControl mViewAnimateControl;

    private FoldStateLiveData mFoldStateLiveData = null;
    private Observer<Boolean> mFoldStateObserver = null;
    /*是否需要生成摘要，用于记录保存弹窗中摘要勾选*/
    private boolean mDoSummary = false;
    private AlertDialog mDisableDialog;
    /*定向录音录制中，外接麦克风导致关闭开关的提示弹窗*/
    private AlertDialog mDirectOffByMicChangedDialog;
    private boolean mIsEnhanceRecord = false;

    /*首次开启定向录音开关，介绍弹窗*/
    private COUIToolTips mTipsDialog;
    private String mDirectRecordTime;
    private Insets mInsets = null;

    private AnimatorSet mEnterAnimation = null;
    private AnimatorSet mExitAnimation = null;
    /*用于记录快速退出当前录制页后，页面异步加载仍然开始了录制，取消录制*/
    private boolean mIsStartRecorded = false;

    private IUnifiedSummaryCallBack mUnifiedSummaryManager;
    private IAsrPluginCallBack mAsrPluginManager;
    private boolean mIsNeedSmartName = false;
    // 判断是否是onReadyService点击的转写按钮
    private boolean mIsClickFromService = false;
    private Consumer<Float> mCircleScaleListener = null;
    private WaveAnimationHelper mWaveAnimationHelper;

    /**
     * 统一管理ASR监听器的注册
     */
    private void tryRegisterAsrListener() {
        if (mRecorderViewModelApi.hasInitRecorderService()) {
            DebugUtil.i(TAG, "tryRegisterAsrListener: registering ASR listener");
            mRecorderViewModelApi.registerRtAsrListener(this);
        }
    }

    /**
     * 统一管理ASR监听器的反注册
     */
    private void tryUnregisterAsrListener() {
        if (mRecorderViewModelApi.hasInitRecorderService()) {
            DebugUtil.i(TAG, "tryUnregisterAsrListener: unregistering ASR listener");
            mRecorderViewModelApi.unregisterRtAsrListener(this);
        }
    }

    @Override
    public void onReadyService() {
        try {
            DebugUtil.i(TAG, "===========>onReadyService");
            if (mNeedOnlyBindService) { // 非该页面启动service，从service获取录制的模式
                mTypePosition = mRecorderViewModelApi.getRecordType();
            }

            // Service启动完成后，尝试注册ASR监听器
            tryRegisterAsrListener();
            // 若 AI录音助手开关打开，则默认开启转写
            setTranscriptionCanClick();
            if (mSmartNameAction.isSmartNameSwitchOpen(BaseApplication.getAppContext())) {
                // 模拟点击转写按钮，因为开启转写有一系列网络和权限检查
                DebugUtil.d(TAG, "onReadyService clickTranscriptionTv");
                mIsClickFromService = true;
                clickBtnTranscriptionBefore();
            }
            if (getIntent().getBooleanExtra(RecorderDataConstant.FROM_GESTURE, false)) {
                if (mRecorderViewModelApi.getCurrentStatus() == HALT_ON) {
                    mRecorderViewModelApi.start();
                }
            }
            //刷新录制按钮UI
            updateRecordControlView(mRecorderViewModelApi.getCurrentStatus());
            //刷新录制时间
            setTimerTextViewText();
            //刷新波形数据
            refreshWaveData(mWaveRecyclerView);
            if (mRecorderViewModelApi.getCurrentStatus() == PAUSED && mWaveRecyclerView != null) {
                // 处理后台录音暂停后，启动页面，波形有空缺，矫正波形数据
                mWaveRecyclerView.fixAmplitudeWhenPause();
            }
            //刷新标记列表
            if (mMarkListAdapter != null) {
                List<MarkDataBean> marks = mRecorderViewModelApi.getMarkData();
                mMarkListAdapter.setData(marks, () -> {
                    RecorderLayoutUtils.updateWaveViewAndOtherView(this, "after bind service success");
                });
                // 异步同步标记数据到字幕标记插入助手
            }
            if (mNeedOnlyBindService
                    && mRecorderViewModelApi.isNeedShowNotificationPermissionDeniedSnackBar()) {
                //从侧边栏/小布进入录制 首次使用APP并且侧边栏快捷录音场景，拒绝通知权限后，在本次录制流程中进入到录制界面需要展示受阻弹窗
                showRequestNotificationPermissionSnackBarWithoutCheck(this);
                mRecorderViewModelApi.resetNeedShowNotificationPermissionDeniedSnackBar();
            }

            mRecorderViewModelApi.addSourceForNotificationBtnDisabled(
                    (mPictureMarkRecommendHelper == null)
                            ? (new MutableLiveData<>(false))
                            : (mPictureMarkRecommendHelper.isAddPictureMarking()));
            // 标记enable
            LiveData<Boolean> markEnable = mRecorderViewModelApi.getMarkEnabledLiveData();
            if (markEnable != null) {
                markEnable.observe(this, this::setMarkEnable);
            }
            directRecordStatus();
        } catch (Exception ignored) {
            DebugUtil.e(TAG, "ReadyService Exception ");
        }
    }

    private void showSaveDialogFromCube() {
        //mIsAutoShowSave = getIntent().getBooleanExtra(RecorderDataConstant.INTENT_AUTO_SHOW_SAVE, false);
        if (mRecorderViewModelApi.isAlreadyRecording()) {
            clickBtnToSave();
        }
    }

    @Override
    public void onCloseService() {
    }

    @Override
    public void onRecordStatusChange(int state) {
        mTimerHandler.post(() -> {
            if (state == PAUSED) {
                updateRecordControlView(PAUSED);
            } else {
                updateRecordControlView(RECORDING);
                if (state == RECORDING) {
                    if (mRecorderViewModelApi.getLastStatus() != PAUSED) {
                        checkNeedRecordingMuteToast();
                    }
                    dismissSaveDialog();
                }
            }
            if (mPopViewController != null) {
                mPopViewController.onStateChange(state);
            }
        });
    }

    @Override
    public void onRecordCallConnected() {
        updateRedCircleViewEnableAndRecordCallTip();
        /**
         *  当录制状态是暂停时来电,挂断电话后没有录制状态的改变
         *  不会执行onRecordStatusChange方法,因此需要在此更新标记状态
         */
        if (mRecorderViewModelApi.getCurrentStatus() == PAUSED) {
            switchPlayState();
        }
    }

    private void updateRedCircleViewEnableAndRecordCallTip() {
        if (mRecorderViewModelApi.isAudioModeChangePause()) {
            if (mRedCircleView != null) {
                mRedCircleView.setCircleColor(false);
            }
            ToastManager.showLongToast(com.soundrecorder.common.R.string.state_call_record_paused);
        } else {
            if (mRedCircleView != null) {
                mRedCircleView.setCircleColor(true);
            }
            ToastManager.showLongToast(com.soundrecorder.common.R.string.call_record_resuem);
        }
    }

    @Override
    public void onWaveStateChange(int state) {
        if (!mRecorderViewModelApi.hasInitAmplitude()) {
            return;
        }
        long time = mRecorderViewModelApi.getAmplitudeCurrentTime();
        setTimerTextViewText();
        updateMarkEnable(time);

        setWaveRecyclerState(mWaveRecyclerView, time, state);
    }

    private void updateMarkEnable(long time) {
        long lastMarkTime = mRecorderViewModelApi.getLastMarkTime();
        if (lastMarkTime > 0) {
            setMarkEnable((time - lastMarkTime >= MARK_DISABLE_TIME));
        }
    }

    private void setWaveRecyclerState(RecorderWaveRecyclerView mWaveRecyclerView, long time, int state) {
        if (mWaveRecyclerView != null) {
            mWaveRecyclerView.setTotalTime(time);
            switch (state) {
                case WaveState.START:
                    mWaveRecyclerView.startRecordMove();
                    break;
                case WaveState.STOP:
                    mWaveRecyclerView.stopRecorderMove();
                    break;
                default:
                    mWaveRecyclerView.recorderIntervalUpdate();
                    break;
            }
        }
    }

    @Override
    public void onMarkDataChange(int markAction, int errorOrIndex) {
        if (errorOrIndex < 0) {
            return;
        }
        List<MarkDataBean> marks = mRecorderViewModelApi.getMarkData();
        boolean hasAddFlag = (markAction == MarkAction.SINGLE_ADD)
                || (markAction == MarkAction.MULTI_ADD);
        if (mWaveRecyclerView != null) {
            mWaveRecyclerView.setMarkTimeList(marks);
            if (hasAddFlag) {
                mWaveRecyclerView.setAddMarkData(marks.get(errorOrIndex));
                mWaveRecyclerView.addBookMark();
                if (mMarkListAdapter != null) {
                    mMarkListAdapter.dismissMenuPop();
                }
            }
        }
        int index = marks.size() - 1;
        if (mMarkListAdapter != null) {
            if (hasAddFlag) {
                mMarkListAdapter.setShowAnimatorPos(index);
            }
            mMarkListAdapter.setData(marks, () -> {
            });
            // 异步同步标记数据到字幕标记插入助手
        }
    }

    @SuppressWarnings("deprecation")
    @Override
    public void onSaveFileStateChange(int state, @NonNull String fileName,
                                      @Nullable String fullPath,
                                      @Nullable RecoverableSecurityException e) {
        switch (state) {
            case SaveFileState.START_LOADING:
                DebugUtil.i(TAG, "onSaveFileStateChange,SAVE_FILE_START_LOADING");
                long time = mRecorderViewModelApi.getAmplitudeCurrentTime();
                appendDirectEndTime(time, true);
                break;
            case SaveFileState.SHOW_LOADING_DIALOG:
                showSaveProgressDialog();
                DebugUtil.i(TAG, "onSaveFileStateChange,SAVE_FILE_SHOW_LOADING_DIALOG");
                break;
            case SaveFileState.ERROR:
                DebugUtil.i(TAG, "onSaveFileStateChange,SAVE_FILE_ERROR");
                try {
                    if (e != null) {
                        startIntentSenderForResult(
                                e.getUserAction().getActionIntent().getIntentSender(),
                                Constants.REQUEST_CODE_RSE, null, 0, 0, 0);
                        DebugUtil.i(TAG,
                                "onSaveFileStateChange,SAVE_FILE_ERROR startIntentSenderForResult");
                    }
                } catch (IntentSender.SendIntentException se) {
                    DebugUtil.e(TAG,
                            "onSaveFileStateChange, deleteUriInMediaDBWhenException, start intent sender error",
                            se, false);
                }
                break;
            case SaveFileState.SUCCESS:
                DebugUtil.i(TAG, "onSaveFileStateChange,SAVE_FILE_SUCCESS");
                if (TextUtils.isEmpty(fileName)) {
                    DebugUtil.d(TAG, "onSaveFileStateChange, name is null");
                    return;
                }
                DebugUtil.i(TAG, "onSaveFileStateChange success fileName = " + fileName);
                // mFileOverLimit一直为false ？？？
                if (!mFileOverLimit) {
                    DebugUtil.i(TAG, "onSaveFileStateChange success doSummary = " + mDoSummary);
                    if (mDoSummary) {
                        /*加750ms延迟（业务动效400ms+activity-finish动效350ms）：规避字幕弹窗底部有灰色区域动效*/
                        RecorderViewModel.getInstance().doStartSummary(DELAY_TIME_DO_SUMMARY);
                    }
                    dismissSaveFileDialog();
                    exitTransition(fileName, fullPath);
                } else {
                    Intent resultIntent = new Intent();
                    resultIntent.putExtra(RecorderDataConstant.SHOULD_SHOW_EXCEEDING_ALERT, true);
                    setResult(Activity.RESULT_OK, resultIntent);
                    exitTransition(fileName, fullPath);
                }
                break;
            default:
                break;
        }
    }

    /**
     * 满意度
     * 在录制开始时
     * 判断当前是铃声变为静音状态
     * 则先提示用户“录音期间来电和通知不响铃”
     * 提示结束之后再去检测是否要显示图片标记新手提示
     * <p>
     * 如果录制前就处于静音状态
     * 则直接检测是否要显示图片标记新手提示
     */
    private void checkNeedRecordingMuteToast() {
        if (mRecordToastSnackBarControl != null) {
            mRecordToastSnackBarControl.checkNeedRecordingMuteToast();
        }
    }

    /**
     * 拒绝通知权限后，通知权限引导snackbar
     *
     * @param shouldFinish
     */
    @Override
    public void showNotificationPermissionSnackBar(boolean shouldFinish) {
        /*toast显示了再显示通知引导snackbar，若正在显示取消/保存弹窗，则不显示*/
        if (shouldFinish) {
            super.showNotificationPermissionSnackBar(true);
        } else if ((mRecordToastSnackBarControl != null && mRecordToastSnackBarControl.checkCanShowNotificationSnack()
                && !isSaveOrCancelDialogShowing())) {
            super.showNotificationPermissionSnackBar(false);
        } else {
            if (mRecordToastSnackBarControl != null) {
                mRecordToastSnackBarControl.setNeedShowNotificationSnack(
                        PermissionUtils.isNeedShowNotificationPermissionSnackBar(this));
            }
        }
    }

    @Override
    public void onClick(View v) {
        if (ClickUtils.isQuickClick()) {
            return;
        }
        int id = v.getId();
        if (id == R.id.red_circle_icon) {
            DebugUtil.d(TAG, "onClick middleControl");
            mRecorderViewModelApi.switchRecorderStatus(FROM_SWITCH_RECORD_STATUS_NORMAL);
        } else if (id == R.id.left_mark_control) {
            onClickLeftMark();
        }
    }

    private void setEnhanceRecord(boolean isEnhance) {
        mRecorderViewModelApi.setDirectRecordSwitch(isEnhance);
        if (isEnhance) {
            appendDirectStartTime(mRecorderViewModelApi.getAmplitudeCurrentTime());
        } else {
            appendDirectEndTime(mRecorderViewModelApi.getAmplitudeCurrentTime(), false);
        }
        if (!PrefUtil.getBoolean(this, RECORD_ENHANCE_STATUS_KEY, false) && isEnhance) {
            showEnhanceRecordTipsDialog();
            PrefUtil.putBoolean(this, RECORD_ENHANCE_STATUS_KEY, true);
        }
    }

    /**
     * append定向录音startTime
     * 1-2,2-3,3-4,
     *
     * @param startTime
     * @return
     */
    private void appendDirectStartTime(long startTime) {
        if (mDirectRecordTime == null) {
            mDirectRecordTime = startTime + "-";
        } else if (mDirectRecordTime.endsWith(",")) {
            mDirectRecordTime = mDirectRecordTime + startTime + "-";
        } else {
            DebugUtil.d(TAG, "appendDirectRecordTime error");
        }
        mRecorderViewModelApi.setDirectRecordTime(mDirectRecordTime);
        mRecorderViewModelApi.setLastDirectRecordOnTime(startTime);
    }

    /**
     * append定向录音 endTime
     * 1-2,2-3,3-4,
     *
     * @param endTime
     * @return
     */
    private void appendDirectEndTime(long endTime, boolean isSave) {
        if (mDirectRecordTime != null && mDirectRecordTime.endsWith("-")) {
            StringBuilder sb = new StringBuilder();
            sb.append(mDirectRecordTime).append(endTime).append(",");

            if (isSave && sb.lastIndexOf(",") != -1) {
                sb.deleteCharAt(sb.lastIndexOf(","));
            }
            mDirectRecordTime = sb.toString();
            DebugUtil.d(TAG, "appendDirectEndTime endTime:" + mDirectRecordTime);
            mRecorderViewModelApi.setDirectRecordTime(mDirectRecordTime);
        }
    }

    private void onClickLeftMark() {
        boolean isMoreThanMax50 = getPictureMarkDelegate() != null
                && getPictureMarkDelegate().checkAddMarkMoreThanMax();
        if (isMoreThanMax50) {
            ToastManager.showShortToast(BaseApplication.getAppContext(),
                    BaseApplication.getAppContext()
                            .getString(
                                    com.soundrecorder.common.R.string.photo_mark_recommend_mark_limit));
        }
        boolean enable = mRecorderViewModelApi.isMarkEnabledFull();
        long time = mRecorderViewModelApi.getAmplitudeCurrentTime();
        if (enable && time > 0) {
            MarkMetaData markMetaData = new MarkMetaData("", "", time, -1, -1);
            MarkDataSource from = null;
            if (mRecorderViewModelApi.isRealTimeSwitch()) {
                from = MarkDataSource.Converting;
            } else {
                from = MarkDataSource.Normal;
            }
            markMetaData.setFrom(from);
            mRecorderViewModelApi.addMark(markMetaData);
        }
        BuryingPoint.addClickRecordTextMark();
    }

    private void onClickRightMarkPhoto() {
        IPictureMarkDelegate pictureMarkDelegate = getPictureMarkDelegate();
        if (pictureMarkDelegate == null) {
            DebugUtil.d(TAG, "IPictureMarkDelegate is null return");
            return;
        }
        boolean isMoreThanMax50 = pictureMarkDelegate.checkAddMarkMoreThanMax();
        if (isMoreThanMax50) {
            ToastManager.showShortToast(BaseApplication.getAppContext(),
                    BaseApplication.getAppContext()
                            .getString(
                                    com.soundrecorder.common.R.string.photo_mark_recommend_mark_limit));
        }
        boolean enable = mRecorderViewModelApi.isMarkEnabledFull();
        long time = mRecorderViewModelApi.getAmplitudeCurrentTime();
        if (enable && time > 0 && !pictureMarkDelegate.checkAddMarkDuplicated(time)) {
            if (mPictureMarkRecommendHelper != null) {
                mPictureMarkRecommendHelper.launchTakePhotoFromRecorderActivity(time);
            }
        }
        BuryingPoint.recordingAddPictureByCamera();
    }

    private void setTranscriptionCanClick() {
        if (mTranscriptionTv != null) {
            mTranscriptionTv.setEnabled(checkAsrServiceState());
        }
    }

    private boolean checkAsrServiceState() {
        boolean hasPermission = PermissionUtils.hasFuncTypePermission(TYPE_PERMISSION_SMART_SHORTHAND);
        boolean isSupportAsrAndDownload = mAsrPluginManager.isAsrPluginSupportAndDownload(mContext);
        boolean hasAsrService = mRecorderViewModelApi.hasInitRecorderService();
        return hasPermission && isSupportAsrAndDownload && hasAsrService;
    }

    private void onClickBtnTranscription() {
        if (mTranscriptionTv == null) {
            return;
        }
        boolean realTimeSwitch = mRecorderViewModelApi.isRealTimeSwitch();
        if (mSubtitleDataInsertHelper != null) {
            mSubtitleDataInsertHelper.saveOldList(realTimeSwitch);
        }
        updateTranscriptionUIStatus(!realTimeSwitch);
        mRecorderViewModelApi.setRealTimeSwitch(!realTimeSwitch);
        RecorderLayoutUtils.updateWaveViewAndOtherView(this, "onClickBtnTranscription");
    }

    /**
     * 更新转写UI状态 包含转写按钮 波形图 实时字幕控件页面显示的状态
     *
     * @param status true: 开启转写 false: 停止转写
     */
    private void updateTranscriptionUIStatus(boolean status) {
        if (mTranscriptionTv == null || mRealTimeView == null || mWaveGradientView == null) {
            return;
        }
        /*获取当前ASR的状态*/
        boolean asrStartingUp = false;
        if (mRecorderViewModelApi.getRealtimeAsrStatus() instanceof RealTimeAsrStatus) {
            asrStartingUp = ((RealTimeAsrStatus) mRecorderViewModelApi.getRealtimeAsrStatus()).isStartingUp();
        }
        DebugUtil.d(TAG, "updateTranscriptionUIStatus  asrStartingUp=" + asrStartingUp + " status=" + status);

        if (status) {
            /*Asr服务是否启动，没有启动需要手动启动，因为启动需要时间，因此可以提前启动
              因为onReadyService里会tryRegisterAsrListener一次，马上又会调一下这个方法，asrStartingUp来不及更新，重复注册Asr可能会有问题，这样规避一下
            */
            if (!asrStartingUp && !mIsClickFromService) {
                tryRegisterAsrListener();
                mRecorderViewModelApi.externalInitAsr();
            } else {
                DebugUtil.d(TAG, "updateTranscriptionUIStatus is click from service");
                mIsClickFromService = false;
            }
            // 初始化语言选择
            initSelectLanguageView();
        } else {
            if (asrStartingUp && !mSmartNameAction.isSmartNameSwitchOpen(BaseApplication.getAppContext())) {
                tryUnregisterAsrListener();
                mRecorderViewModelApi.externalStopAsr();
            }
        }
        if (mWaveAnimationHelper != null) {
            mWaveAnimationHelper.setRealTimeSwitch(status);
            mWaveAnimationHelper.startAnimation();
        }
        setRecorderTitle();
    }


    /**
     * 根据是否开启实时转写开关，切换当前波形图
     *
     * @param isRealTimeConvertOn 实时转写开关是否开启： true 开启， false 关闭
     */
    public void switchWaveView(boolean isRealTimeConvertOn) {
        if (mWaveGradientView != null) {
            mWaveGradientView.setVisibility(VISIBLE);
            mWaveRecyclerView.changeWaveType(LARGE);
            refreshWaveData(mWaveRecyclerView);
        }
    }

    private IPictureMarkDelegate getPictureMarkDelegate() {
        if (mPictureMarkRecommendHelper != null) {
            return mPictureMarkRecommendHelper.getPictureMarkDelegate();
        }
        return null;
    }

    /**
     * 录制中点击保存按钮保存录音
     */
    private void clickBtnToSave() {
        if (mRecorderViewModelApi.isQuickRecord()) {
            //audio during must more than 700 millisecond
            return;
        }
        mIsStartRecorded = false;
        saveRecorderFile();
        BuryingPoint.addClickSaveRecord(RecorderUserAction.VALUE_SAVE_RECORD);
    }

    private void saveRecorderFile() {
        DebugUtil.i(TAG,
                "saveRecorderFile mFileOverLimit = "
                        + mFileOverLimit + " , mIsNeedResult = "
                        + mIsNeedResult);
        if (!mRecorderViewModelApi.hasInitRecorderService()) {
            DebugUtil.e(TAG, "saveRecorderFile, RecorderService is null!");
            return;
        }
        mRecorderViewModelApi.getRealTimeSubtitleBuryingPointCallback(realTimeSubtitleInstance -> {
            boolean smartNameSwitchOpen = mSmartNameAction.isSmartNameSwitchOpen(BaseApplication.getAppContext());
            boolean realTimeSwitchOpen = realTimeSubtitleInstance.isSwitchEverTurnedOn();
            DebugUtil.d(TAG, "saveRecorderFile smartNameSwitchOpen:" + smartNameSwitchOpen + " realTimeSwitchOpen:" + realTimeSwitchOpen);
            // 实时字幕埋点，如果AI录音助手开关打开，进行埋点；否则只有点击过转写按钮才会埋点
            if (smartNameSwitchOpen || realTimeSwitchOpen) {
                realTimeSubtitleInstance.setBuryingPointValid(true);
                List<?> allAsrContent = mRecorderViewModelApi.getAllAsrContent();
                if (!allAsrContent.isEmpty()) {
                    ConvertContentItem item = (ConvertContentItem) allAsrContent.get(0);
                    DebugUtil.d(TAG, "saveRecorderFile first word time:" + item.getStartTime());
                    /*首字上屏时间*/
                    realTimeSubtitleInstance.setFirstWordTime(item.getStartTime());
                    /*转写成功*/
                    realTimeSubtitleInstance.addReturnSuccess();
                }
            }
        });
        //disableAllClickViews(false);
        String defaultDisplayName = mRecorderViewModelApi.getSampleDisplayName(true);
        pauseRecord();
        if (mFileOverLimit) {
            mDoSummary = false;
            doSaveRecord(defaultDisplayName, "");
            return;
        }
        if (mIsNeedResult) {
            mDoSummary = false;
            finishWithResult();
            return;
        }
        //doPauseClick();
        boolean isFromOtherApp = mIsFromCallUi || mIsNeedResult;
        if (!isFromOtherApp && mUnifiedSummaryManager != null && mSmartNameAction.needShowSmartGuideDialog(mContext)) {
            displaySmartNameDialog(defaultDisplayName);
        } else {
            if (BaseUtil.isRealme()) {
                String title = MediaDBUtils.getTitleByName(defaultDisplayName);
                DebugUtil.i(TAG, "showRecorderFile title = " + title);
                showSaveRecordFileDialog(title);
            } else {
                mIsNeedSmartName = false;
                doSaveRecord(defaultDisplayName, "");
            }
        }
    }

    private void finishWithResult() {
        if (mPressedSave) {
            return;
        }
        mPressedSave = true;
        try {
            if (mRecorderViewModelApi.hasInitRecorderService()) {
                int tmp = mRecorderViewModelApi.getCurrentStatus();
                if (tmp != HALT_ON) {
                    stopHook(mRecorderViewModelApi.stop());
                } else {
                    stopHook(null);
                }
            }
            updateRecordControlView(HALT_ON);
        } catch (Exception e) {
            DebugUtil.d(TAG, e.getMessage());
        }
    }

    private void showSaveRecordFileDialog(final String title, RecorderActivity activity) {
        dismissSaveDialog();
        mVerticalButtonDialog = new SaveFileAlertDialog(title, this, activity);
        mVerticalButtonDialog.show();
    }

    private void showSaveRecordFileDialog(final String title) {
        mTimerHandler.postDelayed(new ActivityRunnable<RecorderActivity>(
                "RecorderActivity.showSaveRecordFileDialogPost", RecorderActivity.this) {
            public void run(RecorderActivity recorderActivity) {
                if (recorderActivity != null) {
                    recorderActivity.showSaveRecordFileDialog(title, recorderActivity);
                }
            }
        }, DELAYS_SHOW_SAVE_RECORD_FILE_DIALOG);
    }

    private void showPluginsDialog() {
        final TranscriptionPluginDownloadedCallback pluginCallback = new TranscriptionPluginDownloadedCallback(this);
        if (mSmartNameAction.isSmartNameSwitchOpen(this)) {
            mUnifiedSummaryManager.showAiUnitPluginsDialog(mContext, pluginCallback, true, false);
        } else {
            mAsrPluginManager.showAIAsrPluginsDialog(mContext, pluginCallback);
        }
    }

    @Override
    public void save(@NonNull String displayName, @NonNull String originalDisplayName) {
        if (mVerticalButtonDialog != null && mVerticalButtonDialog.isSummaryVisible()) {
            mDoSummary = mVerticalButtonDialog.isSummaryChecked();
            // 录制页面显示生成摘要统计
            SummaryStaticUtil.addShowStartSummaryEvent(SummaryStaticUtil.EVENT_FROM_RECORD);
        }
        mIsStartRecorded = false;
        doSaveRecord(displayName, originalDisplayName);
    }

    private void doSaveRecord(@NonNull String displayName, @NonNull String originalDisplayName) {
        try {
            if (!mRecorderViewModelApi.hasInitRecorderService()) {
                DebugUtil.e(TAG, "mRecorderService is null.");
                return;
            }

            int state = mRecorderViewModelApi.getCurrentStatus();
            if ((state == PAUSED) || (state == RECORDING)) {
                long time = mRecorderViewModelApi.getAmplitudeCurrentTime();
                if (time < QUICK_CLICK_INTERVAL) {
                    DebugUtil.i(TAG, "the mRecorderService.getTime() is " + time + ", return");
                    return;
                }
                if (mPressedSave) {
                    return;
                }
                mPressedSave = true;
                appendDirectEndTime(time, true);

                saveRecordInfo(displayName, originalDisplayName);
                DebugUtil.d(TAG, "doSaveRecord, HALT_ON");
                updateRecordControlView(HALT_ON);
                if (mMarkListAdapter != null) {
                    mMarkListAdapter.setData(new ArrayList<>());
                }
            } else {
                DebugUtil.e(TAG, "state not recording or paused. state = " + state);
            }
        } catch (Exception e) {
            DebugUtil.e(TAG, "save record error", e);
        }
    }

    private void showSaveProgressDialog() {
        if (mLoadingDialog == null) {
            mLoadingDialog = new LoadingDialog(this);
        }
        if (!mLoadingDialog.isShowing()) {
            mLoadingDialog.show(com.soundrecorder.common.R.string.is_saving, false, false);
        }
    }

    public void startPlayNow() {
        DebugUtil.v(TAG, "startPlayNow");
        if (mIsDoEnterWaveAnimation) {
            DebugUtil.d(TAG, "startPlayNow, mIsDoEnterWaveAnimation return");
            return;
        }
        try {
            long size = getIntent().getLongExtra(RecorderDataConstant.MAX_SIZE, 0);
            if (mIsNeedResult && (size > 0) && (size < MIN_SIZE_LIMIT)) {
                ToastManager.showShortToast(RecorderActivity.this,
                        com.soundrecorder.common.R.string.limit_too_min);
            } else {
                DebugUtil.v(TAG, "onClick start ");
                if (!mRecorderViewModelApi.hasInitRecorderService()) {
                    startEnterWaveAnimation();
                    mTimerHandler.postDelayed(this::startRecord, DELAY_350);
                    mTimerHandler.postDelayed(this::stopEnterWaveAnimation, DELAY_850);
                } else {
                    DebugUtil.e(TAG, "mRecorderService is null.");
                }
            }
        } catch (Exception e) {
            DebugUtil.d(TAG, e.getMessage());
        }
    }

    private void startEnterWaveAnimation() {
        if (mWaveRecyclerView != null && !BaseUtil.isLightOS()) {
            mIsDoEnterWaveAnimation = true;
            mWaveRecyclerView.startRecordEnterAnimation(System.currentTimeMillis());
        }
    }

    private void stopEnterWaveAnimation() {
        if (mWaveRecyclerView != null && mIsDoEnterWaveAnimation) {
            mIsDoEnterWaveAnimation = false;
            mWaveRecyclerView.stopEnterWaveAnimation();
        }
    }

    @Override
    public void onBackPressed() {
        DebugUtil.d(TAG, "onBackPressed");
        if (mPopViewController.onBackPressed()) {
            return;
        }
        if (ClickUtils.isQuickClick()) {
            return;
        }
        showCancelDialog();
    }

    @Override
    public int getRequestCodeX() {
        if (mPictureMarkRecommendHelper != null) {
            return mPictureMarkRecommendHelper.getRequestCodeX();
        }
        return -1;
    }

    /**
     * 点击智能图片标记浮窗，仅一张图片的时候，跳转图片预览页面
     *
     * @param mPopPictures
     * @param shareView
     */
    public void launchPhotoView(@NotNull List<PopPicture> mPopPictures,
                                @org.jetbrains.annotations.Nullable ImageView shareView) {
        if (mPictureMarkRecommendHelper != null) {
            mPictureMarkRecommendHelper.launchPhotoView(mPopPictures, shareView);
        }
    }

    @Override
    public void onActivityLaunched(int sourceType) {
        switch (sourceType) {
            case IPictureMarkDelegate.SOURCE_CAMERA:
                overridePendingTransition(com.support.appcompat.R.anim.coui_open_slide_enter,
                        com.support.appcompat.R.anim.coui_open_slide_exit);
                break;
            default:
                // do nothing
                break;
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == PermissionUtils.REQUEST_CODE_PERMISSION_TRANSPARENT && mRecordToastSnackBarControl != null) {
            mRecordToastSnackBarControl.setNotificationSnackShowing(false);
        }
    }

    @Override
    public void onCancel() {
        mIsStartRecorded = false;
        OSDKCompatUtils.setConvertFromTranslucent(this);
    }

    @NonNull
    @Override
    public View getRootView() {
        return mRootView;
    }

    @Override
    public boolean canShowReadImageSnackOnResume() {
        if (mRecordToastSnackBarControl != null) {
            return mRecordToastSnackBarControl.checkCanShowReadImageSnack();
        }
        return false;
    }

    @Override
    public void onRecordNameSet(@NonNull String recordName) {
        setRecorderTitle();
    }

    @Override
    public void onDirectRecordingOffByMicChanged() {
        showDirectRecordOffTipDialog();
        if (mToolbar == null) {
            return;
        }
        Menu menu = mToolbar.getMenu();
        if (menu == null) {
            return;
        }
        MenuItem menuItem = menu.findItem(R.id.direct);
        if (menuItem != null) {
            menuItem.setTitle(com.soundrecorder.base.R.string.turn_on_directional_vocal_highlighting);
        }
    }

    /**
     * 定向录音
     */
    private void showDirectRecordOffTipDialog() {
        if ((mDirectOffByMicChangedDialog != null) && (mDirectOffByMicChangedDialog.isShowing())) {
            DebugUtil.w(TAG, "showDirectRecordOffTipDialog isShowing");
            return;
        }
        mDirectOffByMicChangedDialog = new COUIAlertDialogBuilder(this,
                com.support.dialog.R.style.COUIAlertDialog_Bottom)
                .setTitle(com.soundrecorder.common.R.string.specified_direct_recording_exited)
                .setBlurBackgroundDrawable(true)
                .setMessage(
                        com.soundrecorder.common.R.string.directional_voice_highlighting_is_not_available)
                .setNegativeButton(com.soundrecorder.common.R.string.button_ok, null)
                .setCancelable(true)
                .show();
        ViewUtils.INSTANCE.updateWindowLayoutParams(mDirectOffByMicChangedDialog.getWindow());
    }

    private static class MyBroadcastReceiver extends BroadcastReceiver {
        private final WeakReference<RecorderActivity> mActivity;

        public MyBroadcastReceiver(RecorderActivity activity) {
            mActivity = new WeakReference<>(activity);
        }

        @Override
        public void onReceive(Context context, Intent intent) {
            RecorderActivity recorderActivity = mActivity.get();
            if (recorderActivity == null) {
                return;
            }
            recorderActivity.updateRecordControlView(HALT_ON);
            String action = intent.getAction();
            DebugUtil.log(TAG, "onReceive intent.getAction=" + action);
            try {
                switch (action) {
                    case RecorderDataConstant.ACTION_RECORDER_STOP_RECORDER:
                        //需要关闭录制界面
                        recorderActivity.stopFinishRecord(intent);
                        break;
                    case RecorderDataConstant.ACTION_RECORDER_STOP_CANCEL:
                        //取消录制，左上角X，back键
                        recorderActivity.cancelRecordFinish();
                        break;
                    case RecorderDataConstant.ACTION_RECORDER_STOP_AUTO_SAVE:
                        //取消录制，右上角，点击保存
                        recorderActivity.showSaveDialogFromCube();
                        break;
                    default:
                        break;
                }
            } catch (Exception e) {
                DebugUtil.d(TAG, e.getMessage());
            }
        }
    }

    /**
     * 需要关闭录制界面
     * 1.录制存储空间不足异常停止，intent不携带参数
     * 2.录制音频文件时，收到关机广播，需要停止录音，intent携带参数value = Integer.MAX_VALUE
     * 3.录制音频文件时，正在录制的音频文件被异常删除，需要停止录音，intent携带参数value = Integer.MAX_VALUE
     * 4.save录音时，存在录制界面且save不在录制界面触发，此时intent会携带参数key_save_from_where，场景目前有小布，蜻蜓/负屏/桌面卡片
     *
     * @param intent 主要获取携带参数key_save_from_where
     */
    private void stopFinishRecord(Intent intent) {
        int from = intent.getIntExtra(RecorderDataConstant.KEY_SAVE_FROM_WHERE, Integer.MAX_VALUE);
        if (from != RecorderDataConstant.MSG_ARG2_SAVE_RECORD_FROM_ACTIVITY) {
            DebugUtil.d(TAG, "stopFinishRecord, from:" + from);
            finish();
        }
    }

    private void cancelRecordFinish() {
        if (mMarkListAdapter != null) {
            mMarkListAdapter.setData(new ArrayList<>());
        }
        DeleteSoundEffectManager.getInstance().playDeleteSound();
        exitTransition("", null);
    }

    @Override
    public void onRestoreInstanceState(@NonNull Bundle savedInstanceState) {
        DebugUtil.i(TAG, "=========>onRestoreInstanceState");
        super.onRestoreInstanceState(savedInstanceState);
        if (mPictureMarkRecommendHelper != null) {
            mPictureMarkRecommendHelper.onRestoreInstanceState(savedInstanceState);
        }
        //由于调整了录制过程中的标记存储逻辑，新增一个标记存储一个标记，这里的RecorderActvity的重建过程中保存标记逻辑不需要了，可以删除
        finish();
    }

    @Override
    public void onSaveInstanceState(@NonNull Bundle outState) {
        DebugUtil.i(TAG, "=========>onSaveInstanceState");
        //由于调整了录制过程中的标记存储逻辑，新增一个标记存储一个标记，这里的RecorderActvity的重建过程中保存标记逻辑不需要了，可以删除
        super.onSaveInstanceState(outState);
        if (mPictureMarkRecommendHelper != null) {
            mPictureMarkRecommendHelper.onSaveInstanceState(outState);
        }
    }

    @Override
    public void onCreate(Bundle bundle) {
        DebugUtil.i(TAG, "=========>onCreate");
        getWindow().requestFeature(Window.FEATURE_NO_TITLE);
        super.onCreate(bundle);
        RecorderViewModel.getInstance().setRecorderFinish(false);
        mLastAppBounds = ScreenUtil.INSTANCE.getRealBounds(this);
        mLastUnfold = ScreenUtil.INSTANCE.isUnFoldStatusWithMultiWindow(this);
        mContext = this;
        mWeakRecorderActivity = new WeakReference<>(this);
        mRecordToastSnackBarControl = new RecordToastSnackBarControl(this);
        mUnifiedSummaryManager = mSmartNameAction.newUnifiedSummaryManager();
        mAsrPluginManager = mAiAsrManagerAction.newAsrPluginManager();
        getIntentValue();
        checkNeedOnlyBindService();
        checkNotEnterAnimation();
        loadContentViewAsync(false);

        mPressedSave = false;
        mPressHomeKeyFlag = false;
        mPressRecentAppFlag = false;
        registerReceivers();
        fromStatusBarOrNotificationBuryingPoint(getIntent());
        mPopViewController = new ViewModelProvider(this).get(PopViewController.class);
        mPopViewController.setPopViewDataCallback(this);
        mPopViewController.registerImagesPermission(this);

        mPictureMarkRecommendHelper = new RecorderPictureMarkRecommendHelper(this, (bundle != null),
                this);

        // 初始化语言弹窗管理器
        mLanguagePopupMenuManager = new LanguagePopupMenuManager(this);

        getLifecycle().addObserver(mPopViewController);
        registerBackPressed();
    }

    /**
     * 判断按钮是否可用
     */
    private void directRecordStatus() {
        MutableLiveData<Boolean> directRecodingEnableLiveData = RecorderViewModel.getInstance()
                .getDirectRecodingEnableLiveData();
        if (directRecodingEnableLiveData != null) {
            directRecodingEnableLiveData.observe(this, aBoolean -> {
                DebugUtil.d(TAG, "directRecordStatus, directRecodingEnable:" + aBoolean);
            });
        }
        MutableLiveData<Boolean> directRecodingOnLiveData = RecorderViewModel.getInstance()
                .getDirectRecodingOnLiveData();
        if (directRecodingOnLiveData != null) {
            directRecodingOnLiveData.observe(this, aBoolean -> {
                DebugUtil.d(TAG, "directRecordStatus, directRecodingOn:" + aBoolean);
                if (mWaveRecyclerView != null) {
                    mWaveRecyclerView.setEnhanceRecording(aBoolean);
                }
            });
        }
        MutableLiveData<String> directTimeLiveData = RecorderViewModel.getInstance().getDirectTimeLiveData();
        if (directTimeLiveData != null) {
            directTimeLiveData.observe(this, time -> {
                if (mWaveRecyclerView != null) {
                    mWaveRecyclerView.setDirectTime(time);
                }
            });
        }

        MutableLiveData<Long> lastDirectOnTimeLiveData = RecorderViewModel.getInstance().getLastDirectRecordOnTimeLiveData();
        if (lastDirectOnTimeLiveData != null) {
            lastDirectOnTimeLiveData.observe(this, time -> {
                if (mWaveRecyclerView != null) {
                    mWaveRecyclerView.setLastDirectOnTime(time);
                }
            });
        }
    }

    private void getIntentValue() {
        try {
            mIsNeedResult = getIntent().getBooleanExtra(RecorderDataConstant.IS_NEED_RESULT, false);
            mTypePosition = getIntent().getIntExtra(RecorderDataConstant.RECORDER_TYPE,
                    RecordModeUtil.getModeValue());
            mRecorderPosition = getIntent().getIntExtra(RecorderDataConstant.RECORDER_POSITION, 0);
            mIsFromCallUi = getIntent().getBooleanExtra(RecorderDataConstant.FROM_CALL_UI, false);
            DebugUtil.d(TAG,
                    "onCreate, the mIsNeedResult is " + mIsNeedResult
                            + " the mTabPosition is " + mTypePosition);
            // check是否需要处理副屏接续内屏逻辑
            mMiniAppApi.checkMiniAppContinueAction(this, getIntent(),
                    new Function1<AlertDialog, Unit>() {
                        @Override
                        public Unit invoke(AlertDialog alertDialog) {
                            mDisableDialog = alertDialog;
                            return Unit.INSTANCE;
                        }
                    });
            int taskId = getTaskId();
            if (!mIsFromCallUi && ActivityTaskUtils.getMainTaskId() != taskId
                    && ActivityTaskUtils.isTaskEmptyExceptActivity(taskId, this)) {
                /*主进程记录主栈ID，如外屏接续到内屏；卡片、侧边栏进入录制*/
                ActivityTaskUtils.setMainTaskId(taskId);
            }
        } catch (Exception e) {
            DebugUtil.i(TAG, e.getMessage());
        }
    }

    @Override
    protected boolean canShowOpenAllFilePermissionOnResume() {
        return IntentExt.getBooleanValue(getIntent(),
                MiniAppConstant.EXTRA_NAME_SHOW_ALL_FILE_PERMISSION_RESUME, true);
    }

    @Override
    protected String getFunctionName() {
        return FUNCTION_NAME;
    }

    @Override
    protected void userChange() {
        mExecutor.execute(() -> {
            try {
                MediaDBUtils.updateRealSizeAndDuration(mRecorderViewModelApi.getSampleUri());
            } catch (Exception e) {
                DebugUtil.e(TAG, "doUserChange updateRealSizeAndDuration error", e);
            }
        });
        super.userChange();
    }

    @Override
    protected void onFoldStateChanged(int state) {
        super.onFoldStateChanged(state);
        mFoldWindowType = state;
        if (mViewAnimateControl != null) {
            mViewAnimateControl.setFoldWindowType(state);
        }
        showRecordFoldWindowCenter();
        if (mMarkListAdapter != null) {
            mMarkListAdapter.dismissMenuPop();
        }

        DebugUtil.d(TAG, "onFoldStateChanged state = " + state);
        // 监听悬浮状态，则取消定向，并弹toast， 悬浮时，定向按钮置灰；点击按钮显示toast
        if (mRecorderViewModelApi.getDirectRecordEnable()) {
            MenuItem menuItem = null;
            if (mToolbar != null && mToolbar.getMenu() != null && mToolbar.getMenu().findItem(R.id.direct) != null) {
                menuItem = mToolbar.getMenu().findItem(R.id.direct);
            } else {
                return;
            }
            boolean directRecordOn = mRecorderViewModelApi.getDirectRecordOn();
            if (directRecordOn && ((mFoldWindowType == FoldingWindowObserver.SCREEN_HORIZONTAL_HOVER)
                    || (mFoldWindowType == FoldingWindowObserver.SCREEN_VERTICAL_HOVER))) {
                setEnhanceRecord(false);
                // 菜单按钮禁用
                menuItem.setEnabled(false);
                menuItem.setTitle(com.soundrecorder.common.R.string.turn_on_directional_vocal_highlighting);
                ToastManager.showLongToast(this, com.soundrecorder.base.R.string.does_not_support_directional_vocal_highlighting);
            } else {
                menuItem.setEnabled(true);
                menuItem.setTitle(com.soundrecorder.common.R.string.turn_on_directional_vocal_highlighting);
            }
        }
    }

    @Override
    protected void onRestart() {
        super.onRestart();
        // 界面使用startActivityForResult启动才拦截
        if (getRequestCodeX() > 0) {
            overridePendingTransition(com.soundrecorder.common.R.anim.finish_entry,
                    com.soundrecorder.common.R.anim.finish_exit);
        }
    }

    private void registerReceivers() {
        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(RecorderDataConstant.ACTION_RECORDER_STOP_RECORDER);
        intentFilter.addAction(RecorderDataConstant.ACTION_RECORDER_STOP_CANCEL);
        intentFilter.addAction(RecorderDataConstant.ACTION_RECORDER_STOP_AUTO_SAVE);
        LocalBroadcastManager.getInstance(getApplicationContext())
                .registerReceiver(mReceiver, intentFilter);

        new HomeWatchUtils(this, (reason) -> {
            onHomeListener(reason);
            return Unit.INSTANCE;
        });
        if (FeatureOption.isHasSupportDragonfly()) {
            mFoldStateLiveData = new FoldStateLiveData();
            mFoldStateObserver = isFoldClose -> {
                if (isFoldClose) {
                    //关闭
                    finishPictureMarkActivity(true);
                }
            };
            mFoldStateLiveData.observeForever(mFoldStateObserver);
        }
    }

    private void onHomeListener(String reason) {
        if (HomeWatchUtils.SYSTEM_HOME_KEY.equals(reason)) {
            finishPictureMarkActivity(true);
            mIsStartRecorded = false;
            mPressHomeKeyFlag = true;
            DebugUtil.i(TAG, "receive home key event, mIsNeedResult " + mIsNeedResult);
            if (mIsNeedResult) {
                saveRecorderFile();
            }
        } else if (HomeWatchUtils.SYSTEM_RECENT_APPS.equals(reason)) {
            finishPictureMarkActivity(true);
            mIsStartRecorded = false;
            mPressRecentAppFlag = true;
            DebugUtil.i(TAG, "receive recentApp event, mIsNeedResult " + mIsNeedResult);
            if (mIsNeedResult) {
                saveRecorderFile();
            }
        }
        /*保存取消弹窗显示时，不显示图片权限引导弹窗*/
        if (!isSaveOrCancelDialogShowing()) {
            if (mPopViewController != null) {
                mPopViewController.checkReadImagePermissionTips();
            }
            if (mRecordToastSnackBarControl != null) {
                mRecordToastSnackBarControl.setNeedShowReadImageSnack(true);
            }
        }
    }

    private void finishPictureMarkActivity(boolean doBackgroundWhenFinishRequestActivity) {
        if (mPictureMarkRecommendHelper == null) {
            DebugUtil.d(TAG, "finishPictureMarkActivity, pictureHelper is null");
            return;
        }
        //当前是图片选择/图片预览界面则不finish
        boolean isChoose = ActivityTaskUtils.any(this, PictureSelectActivity.class)
                || ActivityTaskUtils.any(this, PhotoViewerActivity.class)
                || ActivityTaskUtils.any(this, PopViewLoadingActivity.class);
        if ((Boolean.TRUE.equals(mPictureMarkRecommendHelper.isAddPictureMarking().getValue()))
                && !isChoose) {
            //需要移除requestCode相关页面
            if (getRequestCodeX() != -1) {
                finishActivity(getRequestCodeX());
                mPictureMarkRecommendHelper.setRequestCodeX(-1);
                if (doBackgroundWhenFinishRequestActivity && (mPopViewController != null)) {
                    mPopViewController.onBackground();
                }
            }
            mPictureMarkRecommendHelper.resetAddPictureMarking();
        }
    }

    private void unregisterReceivers() {
        if (mReceiver != null) {
            LocalBroadcastManager.getInstance(getApplicationContext())
                    .unregisterReceiver(mReceiver);
            mReceiver = null;
        }
        if (mFoldStateLiveData != null && mFoldStateObserver != null) {
            mFoldStateLiveData.removeObserver(mFoldStateObserver);
            mFoldStateObserver = null;
            mFoldStateLiveData = null;
        }
    }

    private void fromStatusBarOrNotificationBuryingPoint(Intent intent) {
        if (intent != null) {
            String action = intent.getAction();
            Bundle extras = intent.getExtras();
            if (extras != null) {
                boolean statusBar = extras.getBoolean(STATUS_BAR_KEY);
                if (statusBar) {
                    BuryingPointUtil.addFromNotification();
                }
            }
            if (!TextUtils.isEmpty(action)
                    && (OplusCompactConstant.STATUS_BAR_OPEN_RECORDER_ACTION_BEFOR.equals(action)
                    || OplusCompactConstant.STATUS_BAR_OPEN_RECORDER_ACTION_AFTER.equals(
                    action))) {
                BuryingPointUtil.addFromStatusBar();
            }
        }
    }

    private void checkNeedOnlyBindService() {
        mNeedOnlyBindService = mRecorderViewModelApi.isAlreadyRecording();
    }

    private void checkNotEnterAnimation() {
        try {
            String action = getIntent().getAction();
            if (action != null) {
                //is derect from statusBarClick
                boolean isFromStatusBarClick = action.equalsIgnoreCase(
                        OplusCompactConstant.STATUS_BAR_OPEN_RECORDER_ACTION_BEFOR)
                        || action.equalsIgnoreCase(
                        OplusCompactConstant.STATUS_BAR_OPEN_RECORDER_ACTION_AFTER);
                mNotEnterAnimation = (isFromStatusBarClick);
                DebugUtil.i(TAG,
                        "checkNotEnterAnimation isFromStatusBarClick: " + isFromStatusBarClick
                                + ", mNotEnterAnimation: " + mNotEnterAnimation);
            } else {
                //click from notifiction channel
                boolean isClickFromNotification = getIntent().getBooleanExtra(
                        RecorderDataConstant.PLAYBACK_VIEWER_EXTRA_STATUSBAR, false);
                mNotEnterAnimation = isClickFromNotification;
                if (!mNotEnterAnimation) {
                    mNotEnterAnimation = getIntent().getBooleanExtra(
                            RecorderDataConstant.INTENT_EXTRA_NO_ENTER_ANIMATION, false);
                }
                DebugUtil.i(TAG,
                        "checkNotEnterAnimation isClickFromNotification: " + isClickFromNotification
                                + ", hasNoViewAttrs: mNotEnterAnimation: " + mNotEnterAnimation);
            }
        } catch (Exception e) {
            DebugUtil.i(TAG, e.getMessage());
        }
    }


    @Override
    protected void onNewIntent(Intent intent) {
        DebugUtil.i(TAG, "========>onNewIntent");
        super.onNewIntent(intent);
        fromStatusBarOrNotificationBuryingPoint(intent);
        if (mPictureMarkRecommendHelper != null) {
            mPictureMarkRecommendHelper.onNewIntent(intent);
        }
        mMiniAppApi.checkMiniAppContinueAction(this, intent,
                new Function1<AlertDialog, Unit>() {
                    @Override
                    public Unit invoke(AlertDialog alertDialog) {
                        mDisableDialog = alertDialog;
                        return Unit.INSTANCE;
                    }
                });
    }

    public void startEnterTransition() {
        DebugUtil.d(TAG, "startEnterTransition, mNotEnterAnimation = " + mNotEnterAnimation);
        if (mNeedOnlyBindService) {
            DebugUtil.i(TAG, "only need bindService, no animation");
            setButtonClickListener();
            setOnApplyWindowInsetsListener();
            return;
        }
        if (isDestroyed() || isFinishing()) {
            DebugUtil.w(TAG, "startEnterTransition RecorderActivity  isDestroyed or isFinishing!");
            return;
        }
        if (mViewAnimateControl != null && mWaveGradientView != null) {
            ViewUtils.updateConstraintHeight(mWaveGradientView,
                    mViewAnimateControl.getWaveHeight());
        }
        if (mNotEnterAnimation) {
            mRecordTop.setAlpha(1);
            DebugUtil.i(TAG, "no mViewAttrs, just startRecordNow");
            if (PermissionUtils.hasRecordAudioPermission()
                    && PermissionUtils.hasReadAudioPermission()
                    &&
                    (PermissionUtils.getNextAction() != PermissionUtils.SHOULD_SHOW_USER_NOTICE)) {
                if (!RecorderViewModel.getInstance().isRecorderFinish()) {
                    startPlayNow();
                }
            }
            setButtonClickListener();
            OSDKCompatUtils.setConvertFromTranslucent(this);
            return;
        }
        mEnterAnimation = TransitionUtils.runEnterAnimation(
                getContentView(),
                mRootView,
                new AnimatorListenerAdapter() {
                    @Override
                    public void onAnimationEnd(Animator animation) {
                        if (PermissionUtils.hasRecordAudioPermission()
                                && PermissionUtils.hasReadAudioPermission()
                                && (PermissionUtils.getNextAction()
                                != PermissionUtils.SHOULD_SHOW_USER_NOTICE)) {
                            DebugUtil.i(TAG, "onAnimationEnd: startPlayNow ");
                            if (!RecorderViewModel.getInstance().isRecorderFinish()) {
                                startPlayNow();
                            }
                        }
                        if (isDestroyed() || isFinishing()) {
                            DebugUtil.w(TAG, "RecorderActivity  isDestroyed or isFinishing!");
                            return;
                        }
                        setButtonClickListener();
                        if (mRecordTop != null) {
                            mRecordTop.setAlpha(1f);
                        }
                        if (mWaveGradientView != null) {
                            mWaveGradientView.setAlpha(1f);
                        }
                        if (mSharedView != null) {
                            mSharedView.setBackgroundResource(
                                    com.soundrecorder.base.R.color.common_background_color);
                            if (mWeakRecorderActivity != null && mWeakRecorderActivity.get() != null) {
                                StatusBarUtil.setNavigationBarColor(mWeakRecorderActivity.get(),
                                        com.soundrecorder.base.R.color.common_background_color);
                            } else {
                                DebugUtil.e(TAG, "mWeakRecorderActivity or mWeakRecorderActivity.get() is null!");
                            }
                        }
                        if (mToolbar != null) {
                            mToolbar.setAlpha(1f);
                        }
                        setOnApplyWindowInsetsListener();
                        OSDKCompatUtils.setConvertFromTranslucent(RecorderActivity.this);
                    }

                    @Override
                    public void onAnimationStart(Animator animation) {
                        if (isDestroyed() || isFinishing()) {
                            DebugUtil.w(TAG, "RecorderActivity  isDestroyed or isFinishing!");
                            return;
                        }
                        if (mRecordTop != null) {
                            mRecordTop.setAlpha(0f);
                        }
                        if (mWaveGradientView != null) {
                            mWaveGradientView.setAlpha(0f);
                        }
                        if (mToolbar != null) {
                            mToolbar.setAlpha(0f);
                        }
                    }
                });
    }

    /**
     * 屏幕折叠状态发生改变
     */
    private void showRecordFoldWindowCenter() {
        RecorderLayoutUtils.updateWaveViewAndOtherView(this, UPDATE_WAVE_FROM_WINDOW_FOLD);
    }

    /**
     * 设置折叠中线位置
     * 居中显示
     */
    private void applyDividerCenter(Insets in) {
        if (in == null) {
            DebugUtil.i(TAG, "applyDividerCenter Insets is null return");
            return;
        }
        Space centerDivider = findViewById(R.id.view_center_divider);
        ConstraintLayout.LayoutParams params
                = (ConstraintLayout.LayoutParams) centerDivider.getLayoutParams();
        params.bottomMargin = in.top - in.bottom;
        centerDivider.setLayoutParams(params);
        DebugUtil.i(TAG, "showRecordFoldWindowCenter  mCenterDivider "
                + "left== " + centerDivider.getLeft() + ""
                + "top == " + centerDivider.getTop() + ""
                + "right == " + centerDivider.getRight()
                + "bottom == " + centerDivider.getBottom());
    }

    public void exitTransition(String fileName, String fullPath) {
        if (isNotNeedCustomExistTopToBottomAnim()) {
            finishActivityWithResult(fileName, fullPath);
            return;
        }
        if (isDestroyed() || isFinishing()) {
            DebugUtil.w(TAG, "exitTransition RecorderActivity  isDestroyed or isFinishing!");
            return;
        }
        OSDKCompatUtils.setConvertToTranslucent(this);
        if (mSharedView != null) {
            mSharedView.setBackgroundResource(R.drawable.recorder_background_gradient);
        }
        mExitAnimation = TransitionUtils.runExitAnimation(
                getContentView(),
                (ViewGroup) mRootView,
                new AnimatorListenerAdapter() {
                    @Override
                    public void onAnimationEnd(Animator animation) {
                        finishActivityWithResult(fileName, fullPath);
                    }

                    @Override
                    public void onAnimationStart(Animator animation) {
                    }
                });
    }

    private void finishActivityWithResult(String fileName, String fullPath) {
        DebugUtil.d(TAG, "finishActivityWithResult");
        mIsStartRecorded = false;
        Intent resultIntent = new Intent();
        resultIntent.putExtra(RecorderDataConstant.SHOULD_SHOW_ADD_ANIMATOR, false);
        resultIntent.putExtra(RecorderDataConstant.RECORDER_POSITION, mRecorderPosition);
        if (!TextUtils.isEmpty(fileName)) {
            resultIntent.putExtra(RecorderDataConstant.SHOULD_AUTO_FIND_SAVE_ITEM, true);
            resultIntent.putExtra(RecorderDataConstant.SHOULD_AUTO_FIND_FILE_NAME, fileName);
        }
        if (!TextUtils.isEmpty(fullPath)) {
            resultIntent.putExtra(RecorderDataConstant.SHOULD_AUTO_FIND_FULL_PATH, fullPath);
        }
        resultIntent.putExtra(RecorderDataConstant.SHOULD_AUTO_NEED_SMART_NAME, mIsNeedSmartName);
        setResult(Activity.RESULT_OK, resultIntent);
        RecorderActivity.this.finish();
    }

    private boolean isNotNeedCustomExistTopToBottomAnim() {
        return ActivityTaskUtils.isEmptyExcludeSelf(this)
                && (mFoldWindowType == SCREEN_HORIZONTAL_HOVER);
    }

    @Override
    public void onDestroy() {
        DebugUtil.i(TAG, "=========>onDestroy");
        ExtKt.restoreRingMode(this);
        super.onDestroy();
        mCircleScaleListener = null;
        RecorderViewModel.getInstance().setRecorderFinish(true);
        if (mIsStartRecorded) {
            //页面异步加载完成已开始录音startRecord，但service还未创建成功，此时页面退出，取消录制
            DebugUtil.w(TAG, "onDestroy, cancelRecord");
            cancelRecord();
            mIsStartRecorded = false;
        }

        if (mRecordTop != null) {
            mRecordTop.animate().cancel();
        }
        if (mTipsDialog != null) {
            mTipsDialog.dismiss();
            mTipsDialog = null;
        }
        TransitionUtils.release();
        cancelEnterAnimation();
        cancelExitAnimation();
        mTimerHandler.removeCallbacksAndMessages(null);

        if (mWaveRecyclerView != null) {
            mWaveRecyclerView.setMaxAmplitudeSource(null);
        }
        DebugUtil.i(TAG, "onDestroy, stop service");
        mRecorderViewModelApi.stopService();
        unregisterReceivers();
        if (mMarkListAdapter != null) {
            mMarkListAdapter.setOnDeleteListener(null);
        }
        if (mExecutor != null) {
            mExecutor.shutdown();
            mExecutor = null;
        }
        releaseDialog();
        mCancelConfirmListener = null;
        if (isFinishing()) {
            forceHideRecordStatusBar();
        }
        mRecorderViewModelApi.removeListener(this);

        // ASR监听器反注册
        tryUnregisterAsrListener();

        getLifecycle().removeObserver(mPopViewController);
        if (mViewAnimateControl != null) {
            mViewAnimateControl.release();
            mViewAnimateControl = null;
        }
        if (mRecordToastSnackBarControl != null) {
            mRecordToastSnackBarControl.release();
            mRecordToastSnackBarControl = null;
        }
        // 释放字幕标记插入助手资源
        if (mSubtitleMarkInsertHelper != null) {
            mSubtitleMarkInsertHelper.release();
            mSubtitleMarkInsertHelper = null;
        }
        // 释放语言弹窗管理器资源
        if (mLanguagePopupMenuManager != null) {
            mLanguagePopupMenuManager.release();
            mLanguagePopupMenuManager = null;
        }
        if (mSubtitleDataInsertHelper != null) {
            mSubtitleDataInsertHelper.release();
            mSubtitleDataInsertHelper = null;
        }
        mWaveAnimationHelper = null;
        mDirectRecordTime = null;
        unRegisterBackPressed();
    }

    private void forceHideRecordStatusBar() {
        mRecorderViewModelApi.cancelRecordNotification();
        mRecorderViewModelApi.forceHideRecordStatusBar(RecordStatusBarUpdater.FROM_SERVICE_END);
    }

    private void cancelAnimation(AnimatorSet animation) {
        if (animation != null) {
            animation.cancel();
        }
    }

    private void cancelExitAnimation() {
        cancelAnimation(mExitAnimation);
        mExitAnimation = null;
    }

    private void cancelEnterAnimation() {
        cancelAnimation(mEnterAnimation);
        mEnterAnimation = null;
    }

    @Override
    public void onPause() {
        DebugUtil.i(TAG, "=========>onPause " + this);
        super.onPause();
        RecordStatusBarUpdater.setHasForeground(false);
    }

    @Override
    public void onStop() {
        DebugUtil.i(TAG, "=========>onStop " + this);
        super.onStop();
        OSDKCompatUtils.setConvertFromTranslucent(this);

        if (mPressHomeKeyFlag) {
            int recordStatus = mRecorderViewModelApi.getCurrentStatus();
            DebugUtil.i(TAG, "on stop get recordState " + recordStatus);
            if (recordStatus != HALT_ON) {
                mPressHomeKeyFlag = false;
                mIsStartRecorded = false;
            }
        }
        if (mPressHomeKeyFlag && mPressRecentAppFlag && mIsNeedResult) {
            finish();
        }
        if (mPopViewController != null) {
            mPopViewController.onBackground();
        }

        tryUnregisterAsrListener();
    }

    @Override
    protected void onStart() {
        super.onStart();
        // 尝试注册ASR监听器（如果Service已就绪）
        tryRegisterAsrListener();
    }

    public void gotoPhotoActivity(List<PopPicture> mPopPictures) {
        List<MarkDataBean> marks = mRecorderViewModelApi.getMarkData();
        Intent intent = new Intent();
        intent.putParcelableArrayListExtra("marks", new ArrayList<>(marks));
        intent.putParcelableArrayListExtra("popPictures", new ArrayList<>(mPopPictures));
        intent.setClass(this, PictureSelectActivity.class);
        if (mPictureMarkRecommendHelper != null) {
            mPictureMarkRecommendHelper.launchPictureSelectFromRecorderActivity(intent);
        }
    }

    @Override
    public void onResume() {
        DebugUtil.i(TAG, "=========>onResume " + this);
        RecordStatusBarUpdater.setHasForeground(true);
        super.onResume();
        if (mRecorderViewModelApi.hasInitRecorderService()) {
            if (mWaveRecyclerView != null) {
                mWaveRecyclerView.setSelectTime(mRecorderViewModelApi.getAmplitudeCurrentTime());
                mWaveRecyclerView.notifyDataSetChanged();
            }
        }
    }

    public void stopHook(String uriString) {
        if (!mIsNeedResult) {
            return;
        }
        DebugUtil.v(TAG, "stopHook =" + uriString);
        if (uriString != null) {
            Intent resultIntent = new Intent();
            resultIntent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
            resultIntent.setData(Uri.parse(uriString));
            setResult(Activity.RESULT_OK, resultIntent);
        } else {
            setResult(Activity.RESULT_CANCELED);
        }
        if (!(mPressHomeKeyFlag && mPressRecentAppFlag)) {
            finish();
        }
    }

    @Override
    public void onConfigurationChanged(@NotNull Configuration newConfig) {
        if (mPopViewController != null) {
            mPopViewController.onConfigurationChangedBefore();
        }
        super.onConfigurationChanged(newConfig);
        TipUtil.dismissSelf(TipUtil.TYPE_PICTURE_MARK);
        if (mPictureMarkRecommendHelper != null) {
            mPictureMarkRecommendHelper.onConfigurationChanged(newConfig);
        }
        RecorderViewModel.getInstance().setRecorderFinish(false);
        releaseCancelDialog();

        boolean isFromOtherApp = mIsFromCallUi || mIsNeedResult;
        String defaultDisplayName = mRecorderViewModelApi.getSampleDisplayName(true);
        if (!isFromOtherApp && mUnifiedSummaryManager != null && mSmartNameAction.needShowSmartGuideDialog(mContext)
                && mUnifiedSummaryManager.isSmartNameGuideDialogShowing()) {
            mUnifiedSummaryManager.releaseAllDialog();
            displaySmartNameDialog(defaultDisplayName);
        }
    }

    /**
     * 智能标题引导弹窗
     */
    private void displaySmartNameDialog(String defaultDisplayName) {
        mUnifiedSummaryManager.showSmartNameGuideDialog(this, new RecorderPluginDownloadCallback(this, defaultDisplayName));
    }

    /**
     * 用户须知弹窗onConfigurationChange的重建需再setContentView后执行
     */
    @Override
    public void onPrivacyPolicyConfigurationChanged(@NonNull Configuration newConfig) {
        DebugUtil.i(TAG, "onConfigurationChanged onPrivacyPolicyConfigurationChanged newConfig is " + newConfig);
        WaveViewUtil.clearAll();
        StatusBarUtil.setStatusBarTransparentAndBlackFont(this, com.soundrecorder.base.R.color.common_background_color);
        if (isInMultiWindowMode()) {
            StatusBarUtil.hideNavigationBar(this);
        }
        EditMarkDialog markRenameDialog = getMarkRenameDialog();
        AlertDialog markDeleteDialog = getMarkDeleteDialog();
        MarkDataBean markDeleteData = getMarkDeleteData();
        if (mMarkListAdapter != null) {
            mMarkListAdapter.dismissMenuPop();
        }
        dismissAlertDialog(mDirectOffByMicChangedDialog);
        if (mTipsDialog != null) {
            mTipsDialog.dismiss();
            mTipsDialog = null;
        }
        if (mViewAnimateControl != null) {
            mViewAnimateControl.release();
        }
        loadContentViewAsync(true);
        if (BaseUtil.isAndroidQ()) {
            // androidQ 上修改字体大小，分屏下拖曳重建后不走onApplyInset，这里强制请求分发一次
            getWindow().getDecorView().requestApplyInsets();
        }

        boolean isDismiss = isCanDismiss();
        DebugUtil.e(TAG, "onConfigurationChanged isCanDismiss:" + isDismiss);
        if (isDismiss) {
            /*
             * 直接取消保存录音弹窗
             */
            dismissSaveFileDialog();
            /*
             * 直接取消标记重名弹窗
             */
            dismissMarkRenameDialog(markRenameDialog);
        } else {
            /*
             * 录制页取消弹窗不重建
             */
            if (mVerticalButtonDialog != null) {
                /*
                 * 保存录音弹窗重建
                 */
                mVerticalButtonDialog.onConfigurationChanged();
            }
            if (markRenameDialog != null) {
                /*
                 * 标记重命名弹窗重建
                 */
                markRenameDialog.onConfigurationChanged();
            }
        }
        rebuildMarkDeleteDialog(markDeleteDialog, markDeleteData);
        if (!mRecorderViewModelApi.hasInitRecorderService()) {
            DebugUtil.e(TAG, "onConfigurationChanged service is null");
        } else {
            updateRecordControlView(mRecorderViewModelApi.getCurrentStatus());
        }
        //must be executed after initView()
        setMarkEnable(mRecorderViewModelApi.isMarkEnabledFull());
        if (RecorderViewModel.getInstance().isSupportDirectRecording()) {
            if (mWaveRecyclerView != null) {
                mWaveRecyclerView.setEnhanceRecording(mRecorderViewModelApi.getDirectRecordOn());
            }
        }
        super.onPrivacyPolicyConfigurationChanged(newConfig);
    }

    /**
     * dialog重建时，会引起软键盘的显示和隐藏，从而导致分屏时window发生变化，onConfigurationChanged()方法会重复调用
     *
     * @return true:能够隐藏弹窗，false：不能隐藏弹窗
     */
    private boolean isCanDismiss() {
        // 排除掉屏幕尺寸变化导致的重建隐藏
        Rect current = ScreenUtil.INSTANCE.getRealBounds(this);
        boolean canDismiss = true;
        if (!mLastAppBounds.equals(current)) {
            mLastAppBounds.set(current);
            canDismiss = false;
        }
        // 如果是折叠屏上面开合导致的重建，则直接隐藏
        boolean isUnfold = ScreenUtil.INSTANCE.isUnFoldStatusWithMultiWindow(this);
        if (mLastUnfold != isUnfold) {
            mLastUnfold = isUnfold;
            canDismiss = true;
        }
        DebugUtil.d(TAG, "isCanDismiss current uiStatus:" + isUnfold + " last:" + mLastUnfold
                + " canDismiss:" + canDismiss);
        return canDismiss;
    }

    /**
     * 隐藏保存文件的弹窗
     */
    private void dismissSaveFileDialog() {
        if ((mVerticalButtonDialog != null) && (mVerticalButtonDialog.isShowing())) {
            mVerticalButtonDialog.dismiss();
            mVerticalButtonDialog = null;
        }
    }

    /**
     * 获取标记的命名弹窗
     */
    private EditMarkDialog getMarkRenameDialog() {
        if (mMarkListAdapter == null) {
            return null;
        }
        return mMarkListAdapter.getRenameMarkDialog();
    }

    /**
     * 隐藏标记的命名弹窗
     */
    private void dismissMarkRenameDialog(EditMarkDialog dialog) {
        if ((dialog != null) && (dialog.isShowing())) {
            dialog.dismiss();
        }
    }

    private AlertDialog getMarkDeleteDialog() {
        if (mMarkListAdapter == null) {
            return null;
        }
        return mMarkListAdapter.getDeleteMarkDialog();
    }

    private MarkDataBean getMarkDeleteData() {
        if (mMarkListAdapter == null) {
            return null;
        } else {
            return mMarkListAdapter.mDeleteMark;
        }
    }

    private void rebuildMarkDeleteDialog(AlertDialog dialog, MarkDataBean data) {
        boolean hasShowing = dialog != null && dialog.isShowing() && data != null;
        if (mMarkListAdapter != null) {
            mMarkListAdapter.dismissDeleteDialog();
            if (hasShowing) {
                mMarkListAdapter.showDeleteMark(this, data);
            }
        }
    }

    private void setMarkEnable(boolean enable) {
        if (mMarkView != null && mMarkView.isEnabled() != enable) {
            mMarkView.setEnabled(enable);
        }
    }

    @SuppressLint("InflateParams")
    private void loadContentViewAsync(boolean isNeedSetClickListener) {
        SafeAsyncLayoutInflater asyncInflater = new SafeAsyncLayoutInflater(this);
        asyncInflater.inflate(R.layout.activity_recorder, null, new RecorderOnInflateFinishedListener(this, isNeedSetClickListener));
    }

    private void onContentViewInflated(View view, boolean isNeedSetClickListener) {
        setContentView(view);
        // 在主线程中处理加载完成的视图
        mInsets = getInsets();
        mToolbar = findViewById(R.id.toolbar);
        initToolbar();
        mRootView = findViewById(R.id.root_view);
        mNavigationViewOnTaskBar = findViewById(R.id.view_task_bar_navigation);

        mWaveRecyclerView = findViewById(R.id.ruler_view);
        mWaveGradientView = findViewById(R.id.wave_gradient_view);
        initViewsExceptWave(isNeedSetClickListener);
        mSelectLanguageTv = findViewById(R.id.select_language_tv);
        mRealTimeView = findViewById(R.id.real_time_view);
        mRealTimeViewDown = findViewById(R.id.real_time_view_down);
        ViewExtKt.initPressFeedback(mRealTimeViewDown);
        setWaveViewVisibility(VISIBLE);
        refreshWaveData(mWaveRecyclerView);
        ViewUtils.doOnLayoutChange(mRootView, (v, newRect, oldRect) -> {
            RecorderLayoutUtils.updateWaveViewAndOtherView(this, "doOnLayoutChange");
            return Unit.INSTANCE;
        });
        updatePadding(mInsets);
        mViewAnimateControl = new RecordViewAnimationControl(mRootView);
        mViewAnimateControl.setFoldWindowType(mFoldWindowType);
        setPermissionGrantedListener(mPermissionGrantedListener);
        startEnterTransition();
        //MarkHelper已经重置了标记记数 MarkerCounter.getInstance().reset();
        if ((!mNotEnterAnimation) && (!mNeedOnlyBindService)) {
            if (mRecordTop != null) {
                mRecordTop.setAlpha(0f);
            }
        }
        if (isNeedSetClickListener) {
            directRecordStatus();
        } else {
            mRecorderViewModelApi.addListener(this);
        }
        initTransUIStatus();
        initSelectLanguageView();
        if (mLanguagePopupMenuManager != null) {
            mLanguagePopupMenuManager.dismiss();
        }
        initGradientView();
        mWaveAnimationHelper = new WaveAnimationHelper(this, mRecorderViewModelApi.isRealTimeSwitch());
    }

    private void initSelectLanguageView() {
        if (mSelectLanguageTv != null) {
            List<String> supportLanguageList = mRecorderViewModelApi.getLangListFromCatchOrSp();
            if (supportLanguageList == null || supportLanguageList.isEmpty()) {
                DebugUtil.w(TAG, "initSelectLanguageView supportLanguageList is null or empty");
                String defaultLanguageCode = LanguageUtil.getAsrDefaultLanguage();
                String defaultLanguageName = LanguageUtil.getLanguageDisplayName(mContext, defaultLanguageCode);
                mSelectLanguageTv.setText(defaultLanguageName);
                mRecorderViewModelApi.setSpeechLanguage(defaultLanguageCode);
                return;
            }
            String curSelectedLanguageCode = mRecorderViewModelApi.getSpeechLanguage();
            String languageName = LanguageUtil.getLanguageDisplayName(mContext, curSelectedLanguageCode);
            if (!supportLanguageList.contains(curSelectedLanguageCode)) {
                DebugUtil.w(TAG, "initSelectLanguageView supportLanguageList not contains curSelectedLanguageCode=" + curSelectedLanguageCode);
                String asrDefaultLanguageCode = LanguageUtil.getAsrDefaultSupportLanguageWithLocal(supportLanguageList);
                mRecorderViewModelApi.setCurSelectedLanguage(asrDefaultLanguageCode);
                mRecorderViewModelApi.setSpeechLanguage(asrDefaultLanguageCode);
                languageName = LanguageUtil.getLanguageDisplayName(mContext, asrDefaultLanguageCode);
            }
            mSelectLanguageTv.setText(languageName);
        }
    }

    private void initTransUIStatus() {
        if (mTranscriptionTv == null) {
            return;
        }
        // 判断Asr插件是否支持，若不支持，不显示转写按钮
        boolean supportAIAsr = mAiAsrManagerAction.loadSupportAIAsr(mContext, false);
        boolean fromOtherAppOrigin = mIsFromCallUi || mIsNeedResult;
        boolean isOpen = FeatureOption.isEnableRealtimeSubtitles();
        DebugUtil.d(TAG, "initTransUIStatus supportAIAsr:" + supportAIAsr + " fromOtherAppOrigin:" + fromOtherAppOrigin + " isOpen:" + isOpen);
        if (isOpen && supportAIAsr && !fromOtherAppOrigin) {
            mTranscriptionBox.setVisibility(VISIBLE);
        }
        updateTranscriptionUIStatus(mRecorderViewModelApi.isRealTimeSwitch());
    }

    private void initGradientView() {
        mCaptionsGradientView = findViewById(R.id.captions_gradient_view);
        mCaptionsRecyclerView = findViewById(R.id.captions_view);
        mCaptionsLoadingView = findViewById(R.id.captions_loading_layout);
        mCaptionsTextView = findViewById(R.id.captions_text_view);
        mCaptionsAnimationView = findViewById(R.id.captions_animation_view);
        mSubtitleDataInsertHelper = new SubtitleDataInsertHelper(RecorderActivity.this,
                mCaptionsGradientView,
                mCaptionsRecyclerView,
                mCaptionsLoadingView,
                mCaptionsAnimationView);
        mSubtitleDataInsertHelper.initRecyclerViewOnTouchEvent();
        mSubtitleDataInsertHelper.showLoadingAnim(false);
    }

    private void initViewsExceptWave(boolean needSetClickListener) {
        DebugUtil.i(TAG, "=========>initViewsExceptWave");
        mRecordTop = findViewById(R.id.recorder_top);
        mSharedView = findViewById(R.id.recorder_header);
        mMarkView = findViewById(R.id.left_mark_control);
        mRecorderSave = findViewById(R.id.recorder_save);
        mTranscriptionTv = findViewById(R.id.transcription_tv);
        mTranscriptionBox = findViewById(R.id.transcription_box);
        if (mTranscriptionBox instanceof View) {
            ViewExtKt.initPressFeedback(mTranscriptionTv);
        }
        mRedCircleView = findViewById(R.id.red_circle_icon);
        if (needSetClickListener) {
            updateRecordControlView(mRecorderViewModelApi.getCurrentStatus());
        }
        long time = mRecorderViewModelApi.getAmplitudeCurrentTime();
        updateMarkEnable(time);
        mMiddleControl = findViewById(R.id.middle_control);
        mMiddleControlShadow = findViewById(R.id.middle_control_shadow);
        mCircleScaleListener = num -> {
            mMiddleControlShadow.updateScaleCircle(num);
        };

        mRecorderSave.setCircleScaleListener(mCircleScaleListener);
        if (needSetClickListener) {
            setButtonClickListener();
        }
        if (mIsNeedResult) {
            mMarkView.setVisibility(INVISIBLE);
        } else {
            mMarkView.setVisibility(VISIBLE);
        }

        mTimerTextView = findViewById(R.id.timerView);
        mTimerTextView.setAccessibilityDelegate(new TimerDelegate(this));

        setTimerTextViewText();
        if (mWeakRecorderActivity != null && mWeakRecorderActivity.get() != null) {
            LinearLayoutManager layoutManager = new LinearLayoutManager(mWeakRecorderActivity.get());
            if (mMarkListAdapter == null) {
                mMarkListAdapter = new MarkListAdapter(this, false);
            }
            if (mSubtitleMarkInsertHelper == null) {
                mSubtitleMarkInsertHelper = new SubtitleMarkInsertHelper();
            }
            final List<MarkDataBean> marks = mRecorderViewModelApi.getMarkData();
            mMarkListAdapter.setData(marks, () -> {
                int size = marks.size();
                if (size > 0) {
                    layoutManager.scrollToPositionWithOffset(size - 1, 0);
                }
            });
            // 异步同步标记数据到字幕标记插入助手
        } else {
            DebugUtil.e(TAG, "mWeakRecorderActivity or mWeakRecorderActivity.get() is null!");
        }
        mMarkListAdapter.setOnDeleteListener(dataBean -> {
            int index = CollectionsKt.indexOf(mRecorderViewModelApi.getMarkData(), dataBean);
            mRecorderViewModelApi.removeMark(index);
            mWaveRecyclerView.setRemoveMarkData(dataBean);
        });

        mMarkListAdapter.setOnRenameMarkListener((data, newMarkText, newImage) -> {
            if (TextUtils.isEmpty(newMarkText) && TextUtils.isEmpty(newImage)) {
                return;
            }
            String markTime = data.getShowTime();
            if (TextUtils.isEmpty(markTime)) {
                return;
            }
            int index = CollectionsKt.indexOf(mRecorderViewModelApi.getMarkData(), data);
            mRecorderViewModelApi.renameMark(newMarkText, newImage, index);
        });
    }

    private void initToolbar() {
        setSupportActionBar(mToolbar);
        ActionBar actionBar = getSupportActionBar();
        if (actionBar != null) {
            actionBar.setTitle("");
            actionBar.setHomeButtonEnabled(true);
            actionBar.setDisplayHomeAsUpEnabled(true);
        }
    }

    private void setButtonClickListener() {
        if (mMarkView != null) {
            mMarkView.setOnClickListener(this);
        }
        if (mRedCircleView != null) {
            mRedCircleView.setOnClickListener(this);
        }
        if (mTranscriptionBox != null && mRealTimeViewDown != null) {
            mTranscriptionTv.setOnClickListener(v -> {
                if (ClickUtils.isFastDoubleClick(ClickUtils.DURATION_500)) {
                    DebugUtil.i(TAG, "mTranscriptionTv isFastDoubleClick");
                    return;
                }
                clickBtnTranscriptionBefore();
            });
            mRealTimeViewDown.setOnClickListener(v -> {
                if (ClickUtils.isFastDoubleClick(ClickUtils.DURATION_500)) {
                    DebugUtil.i(TAG, "mTranscriptionTv isFastDoubleClick");
                    return;
                }
                clickBtnTranscriptionBefore();
            });
        }
        if (mRecorderSave != null) {
            mRecorderSave.setOnClickListener(v -> {
                if (ClickUtils.isFastDoubleClick(ClickUtils.DURATION_500)) {
                    DebugUtil.i(TAG, "mRecorderSave isFastDoubleClick");
                    return;
                }
                clickBtnToSave();
            });
        }
        if (mSelectLanguageTv != null && mLanguagePopupMenuManager != null) {
            mSelectLanguageTv.setOnClickListener(v -> {
                if (ClickUtils.isFastDoubleClick(ClickUtils.DURATION_500)) {
                    DebugUtil.i(TAG, "mSelectLanguageTv isFastDoubleClick");
                    return;
                }
                mLanguagePopupMenuManager.showPopupMenu(mSelectLanguageTv);
            });
        }
    }

    /**
     * 点击转写按钮之前，检查网络，检查权限和插件的下载更新
     */
    private void clickBtnTranscriptionBefore() {
        if (mAsrPluginManager != null) {
            // 判断服务是否已经启动
            if (!mRecorderViewModelApi.hasInitRecorderService()) {
                DebugUtil.w(TAG, "clickTranscriptionTv service not ready");
                return;
            }
            boolean realTimeSwitch = mRecorderViewModelApi.isRealTimeSwitch();
            // 判断点击转写时有没有网络
            if (!realTimeSwitch && NetworkUtils.isNetworkInvalid(mContext)) {
                boolean isOpen = FeatureOption.isEnableRealtimeSubtitles();
                DebugUtil.w(TAG, "clickTranscriptionTv no network isOpen:" + isOpen);
                if (isOpen) {
                    ToastManager.showShortToast(mContext, com.soundrecorder.common.R.string.network_disconnect);
                }
                return;
            }
            if (realTimeSwitch) {
                onClickBtnTranscription();
                return;
            }
            if (PermissionUtils.hasFuncTypePermission(TYPE_PERMISSION_SMART_SHORTHAND)) {
                showPluginsDialog();
            } else {
                showSmartShortHandPrivacyDialog();
            }
        }
    }

    private void refreshWaveData(RecorderWaveRecyclerView waveRecyclerView) {
        DebugUtil.i(TAG, "=========>initWaveView");
        if (mRecorderViewModelApi.hasInitRecorderService() && waveRecyclerView != null) {
            DebugUtil.d(TAG, "mWaveRecyclerView: " + waveRecyclerView.getWaveType());
            waveRecyclerView.setMIsRecording(!mRecorderViewModelApi.isAlreadyRecording());
            long time = mRecorderViewModelApi.getAmplitudeCurrentTime();
            waveRecyclerView.setSelectTime(time);
            waveRecyclerView.setTotalTime(time);
            waveRecyclerView.setAmplitudeList(mRecorderViewModelApi.getAmplitudeList());
            waveRecyclerView.setMarkTimeList(mRecorderViewModelApi.getMarkData());
            waveRecyclerView.setImportantForAccessibility(View.IMPORTANT_FOR_ACCESSIBILITY_NO);
            waveRecyclerView.setEnhanceRecording(mRecorderViewModelApi.getDirectRecordOn());
            waveRecyclerView.setLastDirectOnTime(mRecorderViewModelApi.getLastDirectRecordOnTime());
            waveRecyclerView.setMaxAmplitudeSource(new MaxAmplitudeSource() {
                @Override
                public int getMaxAmplitude() {
                    return mRecorderViewModelApi.getMaxAmplitude();
                }

                @Override
                public long getTime() {
                    return mRecorderViewModelApi.getAmplitudeCurrentTime();
                }

                @Override
                public int getRecorderState() {
                    return mRecorderViewModelApi.getCurrentStatus();
                }
            });
            waveRecyclerView.notifyDataSetChanged();
        }
    }

    /**
     * 关闭并置空不需要保存数据的保存弹窗
     */
    private void dismissSaveDialog() {
        if (mVerticalButtonDialog != null) {
            if (mVerticalButtonDialog.isShowing()) {
                mVerticalButtonDialog.dismiss();
            }
            mVerticalButtonDialog = null;
        }
    }

    private void updateRecordControlView(int state) {
        DebugUtil.d(TAG, "updateRecordControlView(), state:" + state);
        if (mRedCircleView != null) {
            switch (state) {
                case HALT_ON:
                    switchPlayState();
                    break;
                case RECORDING:
                    mRedCircleView.switchPauseState();
                    mRedCircleView.setContentDescription(
                            getString(com.soundrecorder.common.R.string.recording_notify_talk_back));
                    break;
                case PAUSED:
                    switchPlayState();
                    mRedCircleView.setContentDescription(
                            getString(com.soundrecorder.common.R.string.record_pause_tips));
                    break;
                default:
                    break;
            }
        }
    }

    private void switchPlayState() {
        //暗色模式下,通话接通时,置灰按钮下的三角形颜色
        if (mRedCircleView != null) {
            if (mRecorderViewModelApi.isAudioModeChangePause()) {
                mRedCircleView.switchCallNightPlayState(NightModeUtil.isNightMode(this));
            } else {
                mRedCircleView.switchPlayState();
            }
        }
    }

    /**
     * 设置波形显示/隐藏
     * 注意：此方法主要用于完全隐藏所有波形图的情况，
     * 大小波形图的切换应该使用switchWaveView方法
     */
    void setWaveViewVisibility(int viewVisibility) {
        if (viewVisibility == GONE) {
            // 完全隐藏所有波形图
            if (mWaveGradientView != null) {
                mWaveGradientView.setVisibility(GONE);
            }
        } else {
            // 显示波形图时，根据当前转写状态决定显示哪个
            if (mRecorderViewModelApi == null) {
                return;
            }
            mWaveGradientView.setVisibility(viewVisibility);
            boolean isRealTimeSwitchOn = mRecorderViewModelApi.isRealTimeSwitch();
            switchWaveView(isRealTimeSwitchOn);
        }
    }

    @Override
    public void finish() {
        super.finish();
        DebugUtil.d(TAG, " finish mIsNeedResult " + mIsNeedResult);
        if (!isNotNeedCustomExistTopToBottomAnim()) {
            if (mIsNeedResult) {
                overridePendingTransition(com.support.appcompat.R.anim.coui_zoom_fade_enter,
                        com.support.appcompat.R.anim.coui_push_down_exit);
            } else {
                overridePendingTransition(0, 0);
            }
        }
        forceHideRecordStatusBar();
    }

    @Override
    public void disableAllClickViews(boolean enable) {
        DebugUtil.i(TAG, "disableAllClickViews enable = " + enable);
        if (mRedCircleView != null) {
            mRedCircleView.setEnabled(enable);
        }
    }

    private static class TimerDelegate extends View.AccessibilityDelegate {
        private final WeakReference<RecorderActivity> mRecorderActivity;

        TimerDelegate(RecorderActivity activity) {
            mRecorderActivity = new WeakReference<>(activity);
        }

        @Override
        public void sendAccessibilityEvent(View host, int eventType) {
            if (eventType == AccessibilityEvent.TYPE_VIEW_ACCESSIBILITY_FOCUSED) {
                RecorderActivity recorderActivity = mRecorderActivity.get();
                if (recorderActivity == null) {
                    return;
                }
                long position = KoinInterfaceHelper.INSTANCE.getRecorderViewModelApi().getAmplitudeCurrentTime();
                host.setContentDescription(
                        TimeUtils.getDurationHint(recorderActivity.getApplicationContext(),
                                position));
            }
            super.sendAccessibilityEvent(host, eventType);
        }
    }

    private void startRecord() {
        try {
            DebugUtil.i(TAG, "startRecord");
            if (!mRecorderViewModelApi.hasInitRecorderService()) {
                mIsStartRecorded = true;
                mRecorderViewModelApi.startRecorderService(intent -> {
                    intent.putExtra(RecorderDataConstant.IS_NEED_RESULT, mIsNeedResult);
                    long maxSize = getIntent().getLongExtra(RecorderDataConstant.MAX_SIZE, 0);
                    long maxDuration = getIntent().getIntExtra(Media.DURATION, 0);
                    String launchFrom = getIntent().getStringExtra(
                            RecorderDataConstant.PAGE_FROM_NAME);
                    if (TextUtils.isEmpty(launchFrom)) {
                        launchFrom = RecorderDataConstant.PAGE_FROM_LAUNCHER;
                    }
                    int inputRecordType = getIntent().getIntExtra(
                            RecorderDataConstant.RECORDER_TYPE, 0);
                    intent.putExtra(RecorderDataConstant.RECORDER_TYPE, mTypePosition);
                    intent.putExtra(RecorderDataConstant.PAGE_FROM_NAME, launchFrom);
                    intent.putExtra(RecorderDataConstant.SERVICE_IS_FROM_OTHER_APP,
                            (mIsFromCallUi || mIsNeedResult));
                    if (maxSize > 0) {
                        intent.putExtra(RecorderDataConstant.SERVICE_MAX_FILE_SIZE, maxSize);
                    } else {
                        intent.putExtra(RecorderDataConstant.SERVICE_MAX_FILE_SIZE, RecorderDataConstant.SERVICE_MAX_FILE_LIMIT_BYTES);
                    }
                    if (maxDuration > 0) {
                        intent.putExtra(RecorderDataConstant.SERVICE_MAX_DURATION, maxDuration);
                    }
                    intent.putExtra(RecorderDataConstant.SERVICE_NEED_RESULT, mIsNeedResult);
                    if (inputRecordType == RECORDER_SET_FORMAT_AMR) {
                        intent.putExtra(RecorderDataConstant.SERVICE_RECORD_FORMAT,
                                RECORDER_AUDIO_FORMAT_AMR_NB);
                    }
                    //正常启动
                    intent.putExtra(RecorderDataConstant.SERVICE_NEED_UPDATE_RECORDCONFIG, true);
                    intent.putExtra(RecorderDataConstant.SERVICE_NEED_UPDATE_OTHERCONFIG, true);
                    intent.putExtra(RecorderDataConstant.SERVICE_AUTO_START_RECORD, true);
                    return Unit.INSTANCE;
                });
            }
        } catch (Exception e) {
            DebugUtil.i(TAG, e.getMessage());
        }
    }

    private void pauseRecord() {
        mRecorderViewModelApi.pause();
    }

    private void cancelRecord() {
        mRecorderViewModelApi.cancel();
    }


    private void saveRecordInfo(@NonNull String displayName, @NonNull String originalDisplayName) {
        mRecorderViewModelApi.saveRecordInfo(displayName, originalDisplayName,
                RecorderDataConstant.MSG_ARG2_SAVE_RECORD_FROM_ACTIVITY, false);
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.menu_direct, menu);
        // 是否支持定向录音功能
        if (mToolbar != null && mToolbar.getMenu() != null && mToolbar.getMenu().findItem(R.id.direct) != null) {
            MenuItem item = mToolbar.getMenu().findItem(R.id.direct);
            item.setVisible(RecorderViewModel.getInstance().isSupportDirectRecording());
            if ((mFoldWindowType == FoldingWindowObserver.SCREEN_HORIZONTAL_HOVER)
                    || (mFoldWindowType == FoldingWindowObserver.SCREEN_VERTICAL_HOVER)) {
                //如果是悬浮状态，禁止设置定向录音
                item.setEnabled(false);
                item.setTitle(com.soundrecorder.common.R.string.turn_on_directional_vocal_highlighting);
            } else {
                if (RecorderViewModel.getInstance().isDirectRecodingOn()) {
                    item.setTitle(com.soundrecorder.common.R.string.turn_off_directional_vocal_highlighting);
                } else {
                    item.setTitle(com.soundrecorder.common.R.string.turn_on_directional_vocal_highlighting);
                }
            }
        }
        return super.onCreateOptionsMenu(menu);
    }

    @Override
    public boolean onOptionsItemSelected(@NonNull MenuItem item) {
        if (ClickUtils.isFastDoubleClick(ClickUtils.DURATION_500)) {
            DebugUtil.i(TAG, "onOptionsItemSelected FastDoubleClick");
            return super.onOptionsItemSelected(item);
        }
        int itemId = item.getItemId();
        if (itemId == android.R.id.home) {
            showCancelDialog();
        } else if (itemId == R.id.direct) {
            if (mRecorderViewModelApi.getDirectRecordEnable()) {
                DebugUtil.d(TAG, "Direct record getDirectRecordEnable 111, mFoldWindowType=" + mFoldWindowType);
                if ((mFoldWindowType == FoldingWindowObserver.SCREEN_HORIZONTAL_HOVER)
                        || (mFoldWindowType == FoldingWindowObserver.SCREEN_VERTICAL_HOVER)) {
                    //如果是悬浮状态，禁止设置定向录音
                    item.setEnabled(false);
                    item.setTitle(com.soundrecorder.common.R.string.turn_on_directional_vocal_highlighting);
                    ToastManager.showLongToast(this, com.soundrecorder.base.R.string.does_not_support_directional_vocal_highlighting);
                } else {
                    BuryingPoint.addClickDirectRecordStatus(
                            mRecorderViewModelApi.getDirectRecordOn()
                                    ? RecorderUserAction.VALUE_DIRECT_RECORD_BUTTON_CLOSE
                                    : RecorderUserAction.VALUE_DIRECT_RECORD_BUTTON_OPEN,
                            mRecorderViewModelApi.getSampleUriStr(),
                            RecorderUserAction.VALUE_DIRECT_FROM_RECORDER_PAGE
                    );
                    boolean directRecodingOn = RecorderViewModel.getInstance().isDirectRecodingOn();
                    MenuItem menuItem = null;
                    if (mToolbar != null && mToolbar.getMenu() != null && mToolbar.getMenu().findItem(R.id.direct) != null) {
                        menuItem = mToolbar.getMenu().findItem(R.id.direct);
                    } else {
                        return super.onOptionsItemSelected(item);
                    }
                    if (directRecodingOn) {
                        menuItem.setTitle(com.soundrecorder.base.R.string.turn_on_directional_vocal_highlighting);
                        ToastManager.showShortToast(mContext, com.soundrecorder.base.R.string.directional_voice_highlighting_is_off);
                    } else {
                        menuItem.setTitle(com.soundrecorder.base.R.string.turn_off_directional_vocal_highlighting);
                        ToastManager.showShortToast(mContext, com.soundrecorder.base.R.string.directional_voice_highlighting_is_on);
                    }
                    setEnhanceRecord(!directRecodingOn);
                }
            } else {
                DebugUtil.d(TAG, "Direct record not enabled，show note toast");
                ToastManager.showShortToast(this, com.soundrecorder.base.R.string.toast_specified_direct_open_fail_not_support);
            }
        }
        return super.onOptionsItemSelected(item);
    }

    private void showCancelDialog() {
        if (!mRecorderViewModelApi.hasInitRecorderService()) {
            DebugUtil.w(TAG, "showCancelDialog finish by service unInit");
            RecorderViewModel.getInstance().setRecorderFinish(true);
            finish();
            return;
        }
        if ((mCancelDialog != null) && (Boolean.TRUE.equals(mCancelDialog.isShowing()))) {
            return;
        }
        if (mCancelConfirmListener == null) {
            mCancelConfirmListener = (dialog, which) -> {
                mIsStartRecorded = false;
                BuryingPoint.addClickCancelRecord(RecorderUserAction.VALUE_EXIT_DELETE_RECORD);
                cancelRecord();
                dialog.dismiss();
            };
        }

        mCancelDialog = new COUIAlertDialogBuilder(this,
                com.support.dialog.R.style.COUIAlertDialog_Bottom)
                .setTitle(com.soundrecorder.common.R.string.recording_exit_confirm_save)
                .setBlurBackgroundDrawable(true)
                .setNeutralButton(com.soundrecorder.common.R.string.recording_exit_save,
                        (dialog, which) -> clickBtnToSave())
                .setPositiveButton(com.soundrecorder.common.R.string.recording_exit_do_not_save, mCancelConfirmListener)
                .setNegativeButton(com.soundrecorder.common.R.string.cancel,
                        (dialogInterface, i) -> BuryingPoint.addClickCancelRecord(
                                RecorderUserAction.VALUE_CANCEL_RECORD))
                .setCancelable(true)
                .show();
        // get neutralButton and set color
        Button neutralButton = mCancelDialog.getButton(DialogInterface.BUTTON_NEUTRAL);
        if (neutralButton != null) {
            // set to system color
            int color = COUIContextUtil.getAttrColor(this, com.support.appcompat.R.attr.couiColorPrimary);
            neutralButton.setTextColor(color);
        }
        ViewUtils.INSTANCE.updateWindowLayoutParams(mCancelDialog.getWindow());
        mDoSummary = false;
    }

    private void showEnhanceRecordTipsDialog() {
        if ((mTipsDialog != null) && (mTipsDialog.isShowing())) {
            return;
        }

        COUIImageBubbleStyleImpl imageBubbleStyleNoEdges = new COUIImageBubbleStyleImpl.Builder()
                .setTitle(getString(com.soundrecorder.common.R.string.direct_record_window_title))
                .loadAnim(R.raw.record_direct_recording_guide)
                .setContentText(getString(com.soundrecorder.common.R.string.direct_record_window_content))
                .setDismissText(getString(com.soundrecorder.common.R.string.button_ok))
                .build();
        mTipsDialog = new COUIToolTips(this, imageBubbleStyleNoEdges);
        mTipsDialog.setDismissOnTouchOutside(true);
        View actionView = mToolbar.getMenuView().findViewById(R.id.direct);
        DebugUtil.i(TAG, "actionView=" + actionView);
        if (actionView != null) {
            mTipsDialog.showWithDirection(actionView, COUIToolTips.ALIGN_TOP);
        }
    }

    private void releaseDialog() {
        dismissSaveDialog();
        if (mLoadingDialog != null) {
            if (mLoadingDialog.isShowing()) {
                mLoadingDialog.dismiss();
            }
            mLoadingDialog = null;
        }
        dismissAlertDialog(mDisableDialog);
        mDisableDialog = null;
        releaseCancelDialog();
        if (mMarkListAdapter != null) {
            mMarkListAdapter.dismissRenameDialog();
            mMarkListAdapter.dismissDeleteDialog();
            mMarkListAdapter.dismissMenuPop();
        }
        dismissAlertDialog(mDirectOffByMicChangedDialog);
        mDirectOffByMicChangedDialog = null;
        if (mUnifiedSummaryManager != null) {
            mUnifiedSummaryManager.releaseAllDialog();
            mUnifiedSummaryManager = null;
        }
    }

    private void dismissAlertDialog(AlertDialog alertDialog) {
        if (alertDialog != null && alertDialog.isShowing()) {
            alertDialog.dismiss();
        }
    }

    private void releaseCancelDialog() {
        dismissAlertDialog(mCancelDialog);
        mCancelDialog = null;
    }

    /**
     * 保存弹窗 or 取消弹窗再显示
     *
     * @return
     */
    private boolean isSaveOrCancelDialogShowing() {
        return ((mCancelDialog != null) ? mCancelDialog.isShowing() : false)
                || ((mVerticalButtonDialog != null) ? mVerticalButtonDialog.isShowing() : false);
    }

    @Override
    public void doPictureMark(@NonNull MarkMetaData pictureMarkMetaData) {
        mRecorderViewModelApi.addMark(pictureMarkMetaData);
    }

    @Override
    public int doMultiPictureMark(@NonNull ArrayList<MarkMetaData> pictureList) {
        return mRecorderViewModelApi.addMultiPictureMark(pictureList);
    }

    @Override
    public void releaseMarks(boolean isFinishing) {
        List<MarkDataBean> marks = mRecorderViewModelApi.getMarkData();
        marks.forEach(dataBean -> dataBean.release(isFinishing));
    }

    @NonNull
    @Override
    public List<Integer> supportPictureMarkSource() {
        return Arrays.asList(IPictureMarkDelegate.SOURCE_CAMERA,
                IPictureMarkDelegate.SOURCE_MULTI_CAMERA);
    }

    @Override
    public long getPlayerCurrentTimeMillis() {
        return mRecorderViewModelApi.getAmplitudeCurrentTime();
    }

    @Nullable
    @Override
    public List<MarkDataBean> getMarkList() {
        return mRecorderViewModelApi.getMarkData();
    }

    @Override
    public long getDuration() {
        // do nothing
        return -1;
    }

    @Override
    public int restorePlayerStatePlaying(int cachePlayerState, boolean hasNeedSpeakOff) {
        // do nothing
        return -1;
    }

    @Override
    public int hasPlayerStatePlaying() {
        // do nothing
        return -1;
    }

    @Override
    public void beforeShowSelectPictureDialog() {
        // do nothing
    }

    /**
     * 添加图片标记完成
     *
     * @param operateCancel 是否取消操作 true：取消； false：未取消，添加到标记
     * @param fromSource    图片来源：相机、相册、后台智能图片标记
     */
    @Override
    public void doSingleOrMultiPictureMarkEnd(boolean operateCancel, int fromSource) {
        switch (fromSource) {
            case IPictureMarkDelegate.SOURCE_ALBUM:
                break;
            case IPictureMarkDelegate.SOURCE_CAMERA:
                if (operateCancel) {
                    BuryingPoint.recordingAddPictureByCameraCancel();
                } else {
                    BuryingPoint.recordingAddPictureByCameraOk();
                }
                break;
            case IPictureMarkDelegate.SOURCE_MULTI_CAMERA:
                if (operateCancel) {
                    BuryingPoint.cancelAddPictureMarkNumber();
                } else {
                    BuryingPoint.oKAddPictureMarkNumber();
                }
                break;
            default:
                // do nothing
                break;
        }
    }


    @Override
    public FrameLayout getContentView() {
        if (isDestroyed() || isFinishing()) {
            DebugUtil.w(TAG, "getContentView RecorderActivity isDestroyed or isFinishing!");
            return null;
        }
        return findViewById(android.R.id.content);
    }

    @NonNull
    @Override
    public View getPictureMarkView() {
        return findViewById(R.id.ib_mark_photo_layout);
    }

    @Override
    public void startActivityForResult(Intent intent, int requestCode) {
        if (mPictureMarkRecommendHelper != null) {
            mPictureMarkRecommendHelper.setRequestCodeX(requestCode);
        }
        super.startActivityForResult(intent, requestCode);
    }

    @Override
    public void startActivityForResult(Intent intent, int requestCode, @Nullable Bundle options) {
        if (mPictureMarkRecommendHelper != null) {
            mPictureMarkRecommendHelper.setRequestCodeX(requestCode);
        }
        super.startActivityForResult(intent, requestCode, options);
    }

    private Insets getInsets() {
        Window window = getWindow();
        if (window != null) {
            WindowCompat.setDecorFitsSystemWindows(window, false);
            View decorView = window.getDecorView();
            if (ViewCompat.getRootWindowInsets(decorView) != null) {
                return ViewCompat.getRootWindowInsets(decorView).getInsets(WindowInsetsCompat.Type.systemBars());
            }
        }
        return Insets.NONE;
    }

    /**
     * 设置到状态栏的间隔
     */
    private void setOnApplyWindowInsetsListener() {
        Window window = getWindow();
        if (window != null) {
            WindowCompat.setDecorFitsSystemWindows(window, false);
            View decorView = window.getDecorView();
            ViewCompat.setOnApplyWindowInsetsListener(decorView, (v, insets) -> {
                Insets in = insets.getInsetsIgnoringVisibility(
                        WindowInsetsCompat.Type.systemBars());
                DebugUtil.d(TAG, "doOnApplyWindowInsets:" + in.toString());
                updatePadding(in);
                return WindowInsetsCompat.CONSUMED;
            });
        }
    }

    @RequiresApi(api = Build.VERSION_CODES.VANILLA_ICE_CREAM)
    private void updateTimerTextViewText(Long formatTime) {
        SpannableStringBuilder text = ExtKt.currentInMsFormatTimeWithColorSpan(formatTime, mContext);
        if (text.length() <= 0) {
            text = SpannableStringBuilder.valueOf(TimeUtils.getFormatTimeExclusiveMill(formatTime));
        }
        // 更新大波形图的时间显示
        if (mTimerTextView != null) {
            ViewUtils.fixTextFlash(mTimerTextView, text);
            mTimerTextView.setContentDescription(text);
        }
    }

    private void setTimerTextViewText() {
        Long formatTime = mRecorderViewModelApi.getAmplitudeCurrentTime();
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.VANILLA_ICE_CREAM) {
             /*
             时间标签上显示的非精确时间，是在准确时间上随机加上了一个50以内的随机数，
             以解决时间格式化时小数点后末位数字无变化的问题
             */
            if (formatTime > 0) {
                long rand = new Random().nextLong(NumberConstant.NUM_50);
                formatTime += rand;
            }
            updateTimerTextViewText(formatTime);

            /*
             * 延迟50ms在更新一次faketime，解决时间变化频率太慢的视觉感受
             */
            if (formatTime > 0) {
                long rand = new Random().nextLong(NumberConstant.NUM_50);
                formatTime += rand;
            }
            final long fakeTime = formatTime;
            if (mTimerHandler != null) {
                mTimerHandler.postDelayed(new ActivityRunnable<>(
                        "", this) {
                    public void run(RecorderActivity recorderActivity) {
                        if (recorderActivity != null) {
                            recorderActivity.updateTimerTextViewText(fakeTime);
                        }
                    }
                }, NumberConstant.NUM_50);
            }
        }
    }

    private void setRecorderTitle() {
        if (mToolbar == null) {
            return;
        }
        mToolbar.post(() -> {
            mToolbar.setTitle(mRecorderViewModelApi.getRecordModeName());
        });
    }

    @Override
    public void onConfigurationChanged() {
    }

    public void updatePadding(Insets in) {
        mRootView.setPadding(in.left, in.top, in.right, in.bottom);
        mSharedView.setPadding(in.left, in.top, in.right, in.bottom);
        applyDividerCenter(in);
        /*由于录制window是透明主题，通过直接设置 navigationBar的颜色无效，这里通过添加底部一个view去处理；
         * 不支持taskbar、不显示taskbar该view是GONE的*/
        if (TaskBarUtil.isTaskBarShowing(in.bottom, this)) {
            mNavigationViewOnTaskBar.getLayoutParams().height = in.bottom;
            mNavigationViewOnTaskBar.setVisibility(VISIBLE);
        } else {
            mNavigationViewOnTaskBar.setVisibility(GONE);
        }
        // 获取 WindowInsetsController
        WindowInsetsControllerCompat windowInsetsController = WindowCompat.getInsetsController(getWindow(), getWindow().getDecorView());
        if (ScreenUtil.checkNeedSetFitWindowsFalse(this)) {
            // 隐藏导航栏
            windowInsetsController.hide(WindowInsetsCompat.Type.navigationBars());
            DebugUtil.d(TAG, "updatePadding: hide navigationBar");
        } else {
            windowInsetsController.show(WindowInsetsCompat.Type.navigationBars());
            DebugUtil.d(TAG, "updatePadding: show navigationBar");
        }
    }

    private static class RecorderPluginDownloadCallback implements IPluginDownloadCallback {
        private final WeakReference<RecorderActivity> mWeakActivity;
        private final String mDefaultDisplayName;

        RecorderPluginDownloadCallback(RecorderActivity recorderActivity, String mDefaultDisplayName) {
            this.mWeakActivity = new WeakReference<>(recorderActivity);
            this.mDefaultDisplayName = mDefaultDisplayName;
        }

        @Override
        public void onDownLoadResult(boolean result) {
            RecorderActivity recorderActivity = mWeakActivity.get();
            if (recorderActivity == null || recorderActivity.isDestroyed()) {
                return;
            }

            recorderActivity.mIsNeedSmartName = result;
            if (PermissionUtils.hasFuncTypePermission(TYPE_PERMISSION_SMART_SHORTHAND)) {
                recorderActivity.doSaveRecord(this.mDefaultDisplayName, "");
            } else {
                recorderActivity.showSmartShortHandPrivacyDialogForSave(mDefaultDisplayName);
            }
        }
    }

    private static class TranscriptionPluginDownloadedCallback implements IPluginDownloadCallback {
        private final WeakReference<RecorderActivity> mWeakActivity;

        TranscriptionPluginDownloadedCallback(RecorderActivity recorderActivity) {
            this.mWeakActivity = new WeakReference<>(recorderActivity);
        }

        @Override
        public void onDownLoadResult(boolean result) {
            DebugUtil.d(TAG, "TranscriptionPluginDownloadedCallback onDownLoadResult=" + result);
            RecorderActivity recorderActivity = mWeakActivity.get();
            if (recorderActivity == null || recorderActivity.isDestroyed()) {
                return;
            }
            if (result) {
                /*在下面执行开启转写的后续操作*/
                boolean isOpen = FeatureOption.isEnableRealtimeSubtitles();
                if (isOpen) {
                    recorderActivity.runOnUiThread(recorderActivity::setTranscriptionCanClick);
                    recorderActivity.runOnUiThread(recorderActivity::onClickBtnTranscription);
                } else {
                    boolean asrStartingUp = RecorderViewModel.getInstance().getRealtimeAsrStatus().isStartingUp();
                    if (!asrStartingUp && !recorderActivity.mIsClickFromService) {
                        recorderActivity.tryRegisterAsrListener();
                        RecorderViewModel.getInstance().externalInitAsr();
                    }
                }
            }
        }
    }


    /**
     * 实时ASR数据更新回调，当前在后台线程
     *
     * @param cache 缓存
     */
    @Override
    public void onSubtitleUpdated(@NonNull IRealtimeSubtitleCache cache) {
        if (mSubtitleMarkInsertHelper != null) {
            mSubtitleMarkInsertHelper.asyncUpdateSubtitleContent(
                    cache.getGeneratedSubtitles(),
                    cache.getTemporySubtitles(),
                    subtitles -> {
                        // 待补充后续实现 - 处理字幕更新完成后的UI更新逻辑
                        runOnUiThread(() -> {
                            if (mSubtitleDataInsertHelper != null) {
                                mSubtitleDataInsertHelper.setAdapterData(subtitles);
                            }
                        });
                    }
            );
            if ((mSubtitleDataInsertHelper != null) && !mRecorderViewModelApi.isRealTimeSwitch()) {
                mSubtitleDataInsertHelper.addInvialList(cache);
            }
        }
    }

    /**
     * 实时ASR状态回调，当前在后台线程
     *
     * @param code
     */
    @Override
    public void onAsrStatus(int code) {
        DebugUtil.i(TAG, "onAsrStatus: code=" + code);
        // 状态回来不管成功还是失败，都将这个值置为false，这样下次点击转写按钮可以正确判断注册的状态。避免AI录音助手开启，插件没下载的情况导致onReadyService注册失败后不注册的情况
        mIsClickFromService = false;
        runOnUiThread(() -> {
            if (mSubtitleDataInsertHelper != null) {
                mSubtitleDataInsertHelper.onAsrStatus(code);
            }
        });
    }

    /**
     * 获取实时ASR支持语种的错误回调，当前在后台线程
     *
     * @param errorCode 错误码
     * @param errorMsg  错误信息
     */
    @Override
    public void onTranslationCfgError(int errorCode, @Nullable String errorMsg) {
        DebugUtil.i(TAG, "onTranslationCfgError: errorCode=" + errorCode + " errorMsg=" + errorMsg);
        mRecorderViewModelApi.setSpeechLanguage(mRecorderViewModelApi.getCurSelectedLanguage());
        // 重新刷新语种
        runOnUiThread(this::initSelectLanguageView);
    }

    /**
     * 获取实时ASR支持语种的政工回调，当前在后台线程
     *
     * @param data 支持的语种
     */
    @Override
    public void onTranslationCfgSuccess(@NonNull Map<String, String> data) {
        DebugUtil.i(TAG, "onTranslationCfgSuccess: data=" + data);
        if (!data.isEmpty()) {
            ArrayList<String> langList = new ArrayList<>(data.keySet());
            PrefUtil.putStringSet(BaseApplication.getAppContext(), PrefUtil.OPLUS_AI_TEXT_ASR_SUPPORT_LANGUAGE_LIST, new HashSet<>(langList));
            RecorderViewModel.getInstance().setAsrSupportLanguageList(langList);
            mRecorderViewModelApi.setSpeechLanguage(mRecorderViewModelApi.getCurSelectedLanguage());
            // 重新刷新语种
            runOnUiThread(this::initSelectLanguageView);
        }
    }

    public void showSmartShortHandPrivacyDialog() {
        final IPrivacyPolicyDelegate privacyDelegate = getPrivacyPolicyDelegate();
        if (privacyDelegate == null) {
            DebugUtil.d(TAG, "showSmartShortHandPrivacyDialog invalid privacyDelegate");
            return;
        }

        privacyDelegate.resumeShowDialog(TYPE_PERMISSION_SMART_SHORTHAND, true, null);
    }

    public void showSmartShortHandPrivacyDialogForSave(final String displayName) {
        PrivacyPolicyApiExtKt.resumeCommonShowDialog(
                getPrivacyPolicyApi(),
                this,
                TYPE_PERMISSION_SMART_SHORTHAND,
                true,
                null,
                () -> {
                    doSaveRecord(displayName, "");
                    return Unit.INSTANCE;
                },
                () -> {
                    doSaveRecord(displayName, "");
                    return Unit.INSTANCE;
                }
        );
    }

    @Override
    public void onPrivacyPolicyFail(int type, @Nullable Integer pageFrom) {
        super.onPrivacyPolicyFail(type, pageFrom);
        if (type == TYPE_PERMISSION_SMART_SHORTHAND) {
            DebugUtil.d(TAG, "onPrivacyPolicyFail");
            if (mSmartNameAction != null) {
                mSmartNameAction.setSmartNameSwitchStatus(mContext, false, false);
            }
        }
    }

    @Override
    public void onPrivacyPolicySuccess(int type, @Nullable Integer pageFrom) {
        super.onPrivacyPolicySuccess(type, pageFrom);
        if (type == TYPE_PERMISSION_SMART_SHORTHAND) {
            DebugUtil.d(TAG, "onPrivacyPolicySuccess");
            if (mSmartNameAction != null) {
                mSmartNameAction.setSmartNameSwitchStatus(mContext, true, false);
                showPluginsDialog();
            }
        }
    }


    /**
     * 语种切换
     */
    public void onLanguageChanged(String code) {
        DebugUtil.d(TAG, "onLanguageChanged: code=" + code);
        if (mSubtitleDataInsertHelper != null) {
            mSubtitleDataInsertHelper.onLanguageChanged(code);
        }
        mRecorderViewModelApi.setSpeechLanguage(code);
    }

    public RecordViewAnimationControl getViewAnimateControl() {
        return mViewAnimateControl;
    }

    public TextView getTimerTextView() {
        return mTimerTextView;
    }

    public TextView getTranscriptionTv() {
        return mTranscriptionTv;
    }

    public LinearLayout getTranscriptionBox() {
        return mTranscriptionBox;
    }

    public LinearLayout getRecordTop() {
        return mRecordTop;
    }

    public ViewGroup getMiddleControl() {
        return mMiddleControl;
    }

    public LinearLayout getRealTimeView() {
        return mRealTimeView;
    }

    public WaveViewGradientLayout getWaveGradientView() {
        return mWaveGradientView;
    }

    public RecorderServiceInterface getRecorderViewModelApi() {
        return mRecorderViewModelApi;
    }

    private static class RecorderOnInflateFinishedListener implements AsyncLayoutInflater.OnInflateFinishedListener {
        private final WeakReference<RecorderActivity> mWeakActivity;
        private final boolean mIsNeedSetClickListener;

        public RecorderOnInflateFinishedListener(RecorderActivity activity, boolean isNeedSetClickListener) {
            this.mWeakActivity = new WeakReference<>(activity);
            this.mIsNeedSetClickListener = isNeedSetClickListener;
        }

        @Override
        public void onInflateFinished(@NonNull View view, @LayoutRes int resid, @Nullable ViewGroup parent) {
            RecorderActivity activity = mWeakActivity.get();
            if (activity == null || activity.isDestroyed()) {
                return;
            }
            activity.onContentViewInflated(view, mIsNeedSetClickListener);
        }
    }
}
