package com.soundrecorder.record.views

import android.content.Context
import android.content.Intent
import android.content.SharedPreferences
import android.os.Bundle
import android.text.TextUtils
import android.view.View
import android.widget.Button
import android.widget.EditText
import android.widget.LinearLayout
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.core.widget.addTextChangedListener
import com.coui.appcompat.toolbar.COUIToolbar
import com.soundrecorder.record.R

/**
 * 动画参数编辑页面
 * 用于编辑WaveAnimationHelper中的动画参数
 */
class WaveAnimationEditActivity : AppCompatActivity() {

    companion object {
        const val TAG = "WaveAnimationEditActivity"
        
        // SharedPreferences key
        private const val PREF_NAME = "wave_animation_params"
        
        // 动画参数的key
        private const val KEY_REAL_TIME_ALPHA_RESPONSE = "real_time_alpha_response"
        private const val KEY_REAL_TIME_ALPHA_BOUNDS = "real_time_alpha_bounds"
        private const val KEY_TIMER_SIZE_RESPONSE = "timer_size_response"
        private const val KEY_TIMER_SIZE_BOUNDS = "timer_size_bounds"
        private const val KEY_RECORDER_TOP_MARGIN_RESPONSE = "recorder_top_margin_response"
        private const val KEY_RECORDER_TOP_MARGIN_BOUNDS = "recorder_top_margin_bounds"
        private const val KEY_WAVE_HEIGHT_RESPONSE = "wave_height_response"
        private const val KEY_WAVE_HEIGHT_BOUNDS = "wave_height_bounds"
        private const val KEY_WAVE_MARGIN_TOP_RESPONSE = "wave_margin_top_response"
        private const val KEY_WAVE_MARGIN_TOP_BOUNDS = "wave_margin_top_bounds"
        private const val KEY_RULER_ALPHA_RESPONSE = "ruler_alpha_response"
        private const val KEY_RULER_ALPHA_BOUNDS = "ruler_alpha_bounds"
        private const val KEY_TRANSLATION_ALPHA_RESPONSE = "translation_alpha_response"
        private const val KEY_TRANSLATION_ALPHA_BOUNDS = "translation_alpha_bounds"
        
        // 默认值
        private const val DEFAULT_RESPONSE = 0.2f
        private const val DEFAULT_BOUNDS = 0.2f
        
        fun start(context: Context) {
            val intent = Intent(context, WaveAnimationEditActivity::class.java)
            context.startActivity(intent)
        }
    }

    private lateinit var toolbar: COUIToolbar
    private lateinit var scrollContainer: LinearLayout
    private lateinit var confirmButton: Button
    private lateinit var sharedPreferences: SharedPreferences
    
    // 编辑框列表
    private val editTextPairs = mutableListOf<Pair<EditText, EditText>>()
    
    // 动画参数配置
    private val animationParams = listOf(
        Triple("实时转写透明度", KEY_REAL_TIME_ALPHA_RESPONSE, KEY_REAL_TIME_ALPHA_BOUNDS),
        Triple("计时器大小", KEY_TIMER_SIZE_RESPONSE, KEY_TIMER_SIZE_BOUNDS),
        Triple("录制顶部边距", KEY_RECORDER_TOP_MARGIN_RESPONSE, KEY_RECORDER_TOP_MARGIN_BOUNDS),
        Triple("波形高度", KEY_WAVE_HEIGHT_RESPONSE, KEY_WAVE_HEIGHT_BOUNDS),
        Triple("波形顶部边距", KEY_WAVE_MARGIN_TOP_RESPONSE, KEY_WAVE_MARGIN_TOP_BOUNDS),
        Triple("标尺透明度", KEY_RULER_ALPHA_RESPONSE, KEY_RULER_ALPHA_BOUNDS),
        Triple("转写按钮透明度", KEY_TRANSLATION_ALPHA_RESPONSE, KEY_TRANSLATION_ALPHA_BOUNDS)
    )

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_wave_animation_edit)
        
        initSharedPreferences()
        initViews()
        setupToolbar()
        createEditTextPairs()
        loadSavedValues()
        setupConfirmButton()
    }
    
    private fun initSharedPreferences() {
        sharedPreferences = getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE)
    }
    
    private fun initViews() {
        toolbar = findViewById(R.id.toolbar)
        scrollContainer = findViewById(R.id.scroll_container)
        confirmButton = findViewById(R.id.confirm_button)
    }
    
    private fun setupToolbar() {
        toolbar.setTitle("动画参数编辑")
        toolbar.setNavigationOnClickListener {
            finish()
        }
    }
    
    private fun createEditTextPairs() {
        editTextPairs.clear()
        scrollContainer.removeAllViews()
        
        animationParams.forEach { (title, responseKey, boundsKey) ->
            val groupView = createEditTextGroup(title, responseKey, boundsKey)
            scrollContainer.addView(groupView)
        }
    }
    
    private fun createEditTextGroup(title: String, responseKey: String, boundsKey: String): View {
        val groupLayout = LinearLayout(this).apply {
            orientation = LinearLayout.VERTICAL
            setPadding(32, 24, 32, 24)
        }
        
        // 标题
        val titleView = TextView(this).apply {
            text = title
            textSize = 16f
            setTextColor(resources.getColor(android.R.color.black, null))
            setPadding(0, 0, 0, 16)
        }
        groupLayout.addView(titleView)
        
        // Response输入框
        val responseLayout = LinearLayout(this).apply {
            orientation = LinearLayout.HORIZONTAL
            setPadding(0, 0, 0, 8)
        }
        
        val responseLabel = TextView(this).apply {
            text = "Response: "
            textSize = 14f
            layoutParams = LinearLayout.LayoutParams(200, LinearLayout.LayoutParams.WRAP_CONTENT)
        }
        
        val responseEditText = EditText(this).apply {
            hint = "默认: $DEFAULT_RESPONSE"
            inputType = android.text.InputType.TYPE_CLASS_NUMBER or android.text.InputType.TYPE_NUMBER_FLAG_DECIMAL
            layoutParams = LinearLayout.LayoutParams(0, LinearLayout.LayoutParams.WRAP_CONTENT, 1f)
        }
        
        responseLayout.addView(responseLabel)
        responseLayout.addView(responseEditText)
        groupLayout.addView(responseLayout)
        
        // Bounds输入框
        val boundsLayout = LinearLayout(this).apply {
            orientation = LinearLayout.HORIZONTAL
            setPadding(0, 0, 0, 16)
        }
        
        val boundsLabel = TextView(this).apply {
            text = "Bounds: "
            textSize = 14f
            layoutParams = LinearLayout.LayoutParams(200, LinearLayout.LayoutParams.WRAP_CONTENT)
        }
        
        val boundsEditText = EditText(this).apply {
            hint = "默认: $DEFAULT_BOUNDS"
            inputType = android.text.InputType.TYPE_CLASS_NUMBER or android.text.InputType.TYPE_NUMBER_FLAG_DECIMAL
            layoutParams = LinearLayout.LayoutParams(0, LinearLayout.LayoutParams.WRAP_CONTENT, 1f)
        }
        
        boundsLayout.addView(boundsLabel)
        boundsLayout.addView(boundsEditText)
        groupLayout.addView(boundsLayout)
        
        // 分割线
        val divider = View(this).apply {
            setBackgroundColor(resources.getColor(android.R.color.darker_gray, null))
            layoutParams = LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT, 
                1
            )
        }
        groupLayout.addView(divider)
        
        // 保存编辑框对
        editTextPairs.add(Pair(responseEditText, boundsEditText))
        
        return groupLayout
    }
    
    private fun loadSavedValues() {
        animationParams.forEachIndexed { index, (_, responseKey, boundsKey) ->
            if (index < editTextPairs.size) {
                val (responseEditText, boundsEditText) = editTextPairs[index]
                
                val savedResponse = sharedPreferences.getFloat(responseKey, DEFAULT_RESPONSE)
                val savedBounds = sharedPreferences.getFloat(boundsKey, DEFAULT_BOUNDS)
                
                if (savedResponse != DEFAULT_RESPONSE) {
                    responseEditText.setText(savedResponse.toString())
                }
                if (savedBounds != DEFAULT_BOUNDS) {
                    boundsEditText.setText(savedBounds.toString())
                }
            }
        }
    }
    
    private fun setupConfirmButton() {
        confirmButton.setOnClickListener {
            saveValues()
        }
    }
    
    private fun saveValues() {
        val editor = sharedPreferences.edit()
        var hasError = false
        
        animationParams.forEachIndexed { index, (_, responseKey, boundsKey) ->
            if (index < editTextPairs.size) {
                val (responseEditText, boundsEditText) = editTextPairs[index]
                
                val responseText = responseEditText.text.toString().trim()
                val boundsText = boundsEditText.text.toString().trim()
                
                val responseValue = if (TextUtils.isEmpty(responseText)) {
                    DEFAULT_RESPONSE
                } else {
                    try {
                        responseText.toFloat()
                    } catch (e: NumberFormatException) {
                        hasError = true
                        DEFAULT_RESPONSE
                    }
                }
                
                val boundsValue = if (TextUtils.isEmpty(boundsText)) {
                    DEFAULT_BOUNDS
                } else {
                    try {
                        boundsText.toFloat()
                    } catch (e: NumberFormatException) {
                        hasError = true
                        DEFAULT_BOUNDS
                    }
                }
                
                editor.putFloat(responseKey, responseValue)
                editor.putFloat(boundsKey, boundsValue)
            }
        }
        
        if (hasError) {
            Toast.makeText(this, "输入格式错误，已使用默认值", Toast.LENGTH_SHORT).show()
        }
        
        editor.apply()
        Toast.makeText(this, "保存成功", Toast.LENGTH_SHORT).show()
        finish()
    }
}
