/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  AutoDiForAppCard.kt
 * * Description : AutoDiForAppCard
 * * Version     : 1.0
 * * Date        : 2025/04/18
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *    renjiahao                             1.0        create
 ***********************************************************************/
package oplus.multimedia.soundrecorder.di

import com.soundrecorder.modulerouter.AppCardInterface
import oplus.multimedia.soundrecorder.card.api.AppCardApi
import org.koin.dsl.module

object AutoDiForAppCard {
    val appCardModule = module {
        single<AppCardInterface>(createdAtStart = true) {
            AppCardApi
        }
    }
}