package oplus.multimedia.soundrecorder

import android.app.Service
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.IBinder
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.recorderservice.RecordResult

class RecordStateService : Service() {

    companion object {
        const val TAG = "RecordStateService"

        const val START_AND_BIND_RECORDER_SERVICE_ACTION =
            "com.oplus.soundrecorder.breeno.START_FORGROUD_SERVICE"
        const val START_RECORD_FAILED_ACTION = "com.oplus.soundrecorder.breeno.START_RECORD_FAILED"
        const val START_AND_BIND_EXTER = "start_breno_extra"
        const val START_RECORD_FAILED_EXTRA = "breno_failed_extra"
    }

    private var mSoundRecordBinder: SoundRecordBinder = SoundRecordBinder(this)
    private var mBroadcastReceiver: BroadcastReceiver? = null

    override fun onCreate() {
        DebugUtil.i(TAG, "onCreate")
        super.onCreate()
        registerBroadCastReceiver()
    }

    override fun onBind(p0: Intent?): IBinder? {
        DebugUtil.i(TAG, "onBind $p0")
        return mSoundRecordBinder
    }

    override fun onUnbind(intent: Intent?): Boolean {
        DebugUtil.i(TAG, "onUnbind $intent")
        //stopSelf()
        return super.onUnbind(intent)
    }


    private fun registerBroadCastReceiver() {
        DebugUtil.i(TAG, "registerBroadCastReceiver")
        if (mBroadcastReceiver == null) {
            mBroadcastReceiver = object : BroadcastReceiver() {
                override fun onReceive(p0: Context?, p1: Intent?) {
                    val action = p1?.action
                    DebugUtil.i(TAG, "onReceive action $action")
                    if (action != null && action.contentEquals(
                            START_AND_BIND_RECORDER_SERVICE_ACTION
                        )
                    ) {
                        val needStartRecordAfterBind =
                            p1?.extras?.get(START_AND_BIND_EXTER) as Boolean
                        mSoundRecordBinder.bindRecorderServiceAsync(
                            this@RecordStateService,
                            needStartRecordAfterBind
                        )
                    }
                    if (action != null && action.contentEquals(START_RECORD_FAILED_ACTION)) {
                        val recordResult = p1?.getParcelableExtra<RecordResult>(
                            START_RECORD_FAILED_EXTRA
                        )
                        DebugUtil.i(TAG, "receive action $action, recordResult: $recordResult")
                        if (recordResult != null) {
                            mSoundRecordBinder.postResult(recordResult)
                        }
                    }
                }
            }
            val intentFilter = IntentFilter()
            intentFilter.addAction(START_AND_BIND_RECORDER_SERVICE_ACTION)
            intentFilter.addAction(START_RECORD_FAILED_ACTION)
            mBroadcastReceiver?.let {
                LocalBroadcastManager.getInstance(this).registerReceiver(it, intentFilter)
            }
        }
    }


    private fun unRegisterBroadCastReceiver() {
        DebugUtil.i(TAG, "unRegisterBroadCastReceiver")
        if (mBroadcastReceiver != null) {
            mBroadcastReceiver?.let {
                LocalBroadcastManager.getInstance(this).unregisterReceiver(it)
            }
            mBroadcastReceiver = null
        }
    }

    override fun onDestroy() {
        DebugUtil.i(TAG, "onDestroy")
        //主线程
        mSoundRecordBinder.unbindRecorderServiceAsync(this)
        unRegisterBroadCastReceiver()
        super.onDestroy()
    }
}