/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: SmallCardData
 Description:
 Version: 1.0
 Date: 2022/8/29
 Author: W9013204
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9013204 2022/8/29 1.0 create
 */

package oplus.multimedia.soundrecorder.card.small

import androidx.annotation.Keep
import com.soundrecorder.common.databean.markdata.MarkDataBean

@Keep
data class SmallCardData(
    val packageName: String,
    val widgetCode: String,
    val recordState: Int,
    val saveFileState: Int,
    val timeText: String,
    val timeTextColor: Int,
    val timeDes: String,
    val stateText: String,
    val showMarkNotice: Boolean,
    val markEnable: Boolean,
    val isStartServiceFormAppCar: Boolean,
    val fileName: String,
    val fileNameWithOutType: String,
    var markSrc: Int,
    var markBackGroundSrc: Int,
    var saveFileSrc: Int,
    var saveBackGroundSrc: Int,
    var recordInitSrc: Int,
    val recordPauseSrc: Int,
    val recordResumeSrc: Int,
    val waveData: Int,
    val showLoadingDialog: Boolean,
    val progressValue: Int = 0,
    val loadingTitle: String,
    val needRunMarkAndSaveAnimation: Boolean = false,
    val waveRadius: Float = -1F,
    val animatorDuration: Long = -1,
    val ampMinHeight: Int = -1,
    val ampMaxHeight: Int = -1,
    val ampColor: String = "",
    val ampMaxRandomHeight: Int = -1,
    val recordServiceUUID: String = "",
    val markDesc: String = "",
    val recordStateDesc: String = "",
    val saveDesc: String = "",
    val recordTimeMill: Long = 0, // 录制时长毫秒，add in versionCode 3
    val lastMarks: List<MarkDataBean>? = null, // 标记数据，add in versionCode 3
    val lastAmps: List<Int>? = null, // 波形数据，add in versionCode 3
    val ampTotalSize: Int = 0, // 波形总数量，add in versionCode 3
    val cardBgDrawableRes: Int = -1, // 卡片背景颜色，add in versionCode 3
    val loadingTitleTalkBack: String = "", // 保存中TEXT的talkback描述，add in versionCode 3
    val versionCode: Int = -1, // 录音app中卡片版本，add in versionCode 3
)

object RecorderState {
    const val INIT = 0
    const val RECORDING = 1
    const val PAUSED = 2
}

object SaveFileState {
    const val INIT = 0
    const val START_LOADING = 1
    const val SHOW_DIALOG = 2
    const val SUCCESS = 3
    const val ERROR = 4
}

object ClickAction {
    const val CARD_SWITCH_RECORDER_STATUS = "switch_recorder_status"
    const val CARD_ADD_TEXT_MARK = "add_text_mark"
    const val CARD_SAVE_RECORDER_FILE = "save_recorder_file"
    const val CHECK_RECORDER_PERMISSION = "check_recorder_permission"
    const val CHECK_CAN_START_SERVICE = "check_can_start_service"
}

object ActivityAction {
    const val KEY_DO_ACTION = "do_action"
    const val KEY_FILE_NAME = "file_name"
    const val ACTION_SHOW_NO_PERMISSION = "action_show_no_permission"
    const val ACTION_SHOW_SAVE_FILE_SUCCESS = "action_show_save_file_success"
    const val CLEAR_ALL_TASK = "clear_all_Task"
}
