/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: AppCardApi
 Description:
 Version: 1.0
 Date: 2022/12/22
 Author: ********
 -----------Revision History-----------
 <author> <date> <version> <desc>
 ******** 2022/12/22 1.0 create
 */

package oplus.multimedia.soundrecorder.card.api

import android.os.Bundle
import com.soundrecorder.modulerouter.AppCardInterface
import com.soundrecorder.modulerouter.CARD_TYPE_FOR_DRAGON_FLY
import com.soundrecorder.modulerouter.CARD_TYPE_FOR_ONE_PLUS_SMALL_CARD
import com.soundrecorder.modulerouter.CARD_TYPE_FOR_RECOMMEND_SMALL_CARD
import com.soundrecorder.modulerouter.CARD_TYPE_FOR_SMALL_CARD
import com.soundrecorder.modulerouter.WidgetCodeInterface
import com.soundrecorder.modulerouter.utils.Injector
import oplus.multimedia.soundrecorder.card.dragonFly.RecorderAppCardManager
import oplus.multimedia.soundrecorder.card.small.SmallCardManager

object AppCardApi : AppCardInterface {

    val widgetCodeApi by lazy {
        Injector.injectFactory<WidgetCodeInterface>()
    }

    override fun addWidgetCodes(widgetCode: String) {
        when (widgetCodeApi?.getCardType(widgetCode)) {
            CARD_TYPE_FOR_DRAGON_FLY -> RecorderAppCardManager.addWidgetCodes(widgetCode)
            CARD_TYPE_FOR_RECOMMEND_SMALL_CARD, CARD_TYPE_FOR_SMALL_CARD, CARD_TYPE_FOR_ONE_PLUS_SMALL_CARD -> {
                SmallCardManager.addWidgetCodes(widgetCode)
            }
            else -> {}
        }
    }

    override fun addWidgetCodesOnResume(widgetCode: String) {
        when (widgetCodeApi?.getCardType(widgetCode)) {
            CARD_TYPE_FOR_DRAGON_FLY -> RecorderAppCardManager.addWidgetCodesOnResume(widgetCode)
            CARD_TYPE_FOR_RECOMMEND_SMALL_CARD, CARD_TYPE_FOR_SMALL_CARD, CARD_TYPE_FOR_ONE_PLUS_SMALL_CARD -> {
                SmallCardManager.addWidgetCodesOnResume(widgetCode)
            }
            else -> {}
        }
    }

    override fun removeWidgetCodeOnPause(widgetCode: String) {
        when (widgetCodeApi?.getCardType(widgetCode)) {
            CARD_TYPE_FOR_DRAGON_FLY -> RecorderAppCardManager.removeWidgetCodeOnPause(widgetCode)
            CARD_TYPE_FOR_RECOMMEND_SMALL_CARD, CARD_TYPE_FOR_SMALL_CARD, CARD_TYPE_FOR_ONE_PLUS_SMALL_CARD -> {
                SmallCardManager.removeWidgetCodeOnPause(widgetCode)
            }
            else -> {}
        }
    }

    override fun callFromDragonFlyCard(method: String, widgetCode: String): Bundle? {
        return RecorderAppCardManager.parseMethod(method, widgetCode)
    }

    override fun callFromSmallCard(method: String, widgetCode: String, isRecommendCard: Boolean): Bundle? {
        return SmallCardManager.parseMethod(method, widgetCode, isRecommendCard)
    }
}