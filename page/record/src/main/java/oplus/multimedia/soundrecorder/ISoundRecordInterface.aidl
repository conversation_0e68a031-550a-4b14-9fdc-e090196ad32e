// ISoundRecordInterface.aidl
package oplus.multimedia.soundrecorder;

// Declare any non-default types here with import statements

import com.soundrecorder.recorderservice.RecordResult;
import com.soundrecorder.recorderservice.RecordStatus;
import oplus.multimedia.soundrecorder.IRecordActionCallback;

interface ISoundRecordInterface {

    //查询当前录音状态接口
    RecordStatus queryStatus();

    //注册回调接口
    boolean registerCallback(IRecordActionCallback callback);

    //反注册回调接口
    boolean unRegisterCallback(IRecordActionCallback callback);

    //继续录音接口
    void resumeRecord();

    //暂停录音接口
    void pauseRecord();

    //停止录音接口
    void stopRecord();
}