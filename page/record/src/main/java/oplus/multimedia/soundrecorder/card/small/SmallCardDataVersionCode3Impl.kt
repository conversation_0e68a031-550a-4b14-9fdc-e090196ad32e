/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: SmallCardDataUtil
 * Description:
 * Version: 1.0
 * Date: 2023/10/31
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2023/10/31 1.0 create
 */

package oplus.multimedia.soundrecorder.card.small

import android.content.Context
import com.soundrecorder.base.ext.title
import com.soundrecorder.base.utils.TimeUtils
import com.soundrecorder.common.databean.markdata.MarkDataBean
import com.soundrecorder.modulerouter.recorder.RecorderServiceInterface
import com.soundrecorder.modulerouter.utils.Injector
import com.soundrecorder.record.R

object SmallCardDataVersionCode3Impl {
    private const val WAVE_MAX_SIZE = 50
    private const val MARK_MAX_SIZE = 5

    private val recorderViewModelApi by lazy {
        Injector.injectFactory<RecorderServiceInterface>()
    }


    /**
     * 卡片录制时长TEXT、talkback描述、textColor
     * @param time 录制时长
     * @param recordStateValue 录制状态-转换成卡片数据的状态值
     * @return fist：录制时长TEXT
     * @return second：录制时长TEXT的talkback描述
     * @return third：录制时长TEXT的textColor
     */
    @JvmStatic
    fun getRecordTimeTextAndTalkDec(
        context: Context,
        time: Long,
        recordStateValue: Int,
    ): Triple<String, String, Int> {
        // versionCode =3，改成最小为秒 00:00样式
        val recordText = TimeUtils.getFormatTimeExclusiveMill(time)
        val recordTalkBackDec = TimeUtils.getDurationHint(context, time)
        val textColor = when (recordStateValue) {
            RecorderState.RECORDING, RecorderState.PAUSED -> R.color.breeno_record_time_color
            else -> R.color.breeno_record_time_color_init
        }
        return Triple(recordText, recordTalkBackDec, textColor)
    }

    /**
     * 录制按钮相关信息（enable状态、资源ID）、（背景ID、talkback描述）
     * @param originSaveState 保存状态的原始值
     * @param recordStateValue 转成卡片数据中的录制状态值
     * @return
     */
    @JvmStatic
    fun getRecordButtonData(
        context: Context,
        recordStateValue: Int,
        originSaveState: Int,
        pausedAudioChange: Boolean
    ): Pair<Int, String> {
        val srcResourceId: Int
        val talkBackDec: String
        when (recordStateValue) {
            RecorderState.RECORDING -> {
                srcResourceId = if (isSavingState(originSaveState)) {
                    R.drawable.breeno_card_record_recording_enable_new
                } else {
                    R.drawable.breeno_card_record_recording_new
                }
                talkBackDec = context.getString(com.soundrecorder.common.R.string.recording_notify_talk_back)
            }

            RecorderState.PAUSED -> {
                srcResourceId = if (pausedAudioChange || isSavingState(originSaveState)) {
                    R.drawable.ic_breeno_card_record_enable_pause_new
                } else {
                    R.drawable.breeno_card_record_pause_new
                }
                talkBackDec = context.getString(com.soundrecorder.common.R.string.record_pause_tips)
            }

            else -> {
                srcResourceId = R.drawable.breeno_card_record_init_new
                talkBackDec = context.getString(com.soundrecorder.common.R.string.recording_start)
            }
        }
        return Pair(srcResourceId, talkBackDec)
    }


    /**
     * 标记按钮enable状态、资源ID、背景资源ID
     * @return first:enable状态
     *  second：显示资源id
     *  third：背景资源id
     */
    @JvmStatic
    fun getMarkButtonData(): Triple<Boolean, Int, Int> {
        val markEnable = recorderViewModelApi?.isMarkEnabledFull() ?: false
        val markSrc = if (markEnable) {
            R.drawable.breeno_card_record_mark_new
        } else {
            R.drawable.ic_breeno_card_record_enable_mark_new
        }
        return Triple(markEnable, markSrc, R.drawable.small_card_no_ripple_bg)
    }

    /**
     * 保存按钮enable状态、资源ID、背景资源ID
     * @param originSaveState 保存状态的原始值
     * @return first:enable状态
     *  second：显示资源id
     *  third：背景资源id
     */
    @JvmStatic
    fun getSaveButtonData(originSaveState: Int): Triple<Boolean, Int, Int> {
        val isSavingState = isSavingState(originSaveState)
        val saveFileSrc = if (isSavingState) {
            R.drawable.breeno_card_record_save_enable_new
        } else {
            R.drawable.breeno_card_record_save_new
        }

        return Triple(!isSavingState, saveFileSrc, R.drawable.small_card_no_ripple_bg)
    }

    /**
     * 卡片保存成功View的文本内容
     * @param context
     * @param saveStateValue 保存状态，转换成SmallCardData.SaveFileState后的值
     * @param fileName  保存音频名称，含后缀
     */
    @JvmStatic
    fun getSaveSuccessViewData(
        context: Context,
        saveStateValue: Int,
        fileName: String
    ): Pair<String, String> {
        if (saveStateValue != SaveFileState.SUCCESS) {
            return Pair("", "")
        }
        // 卡片versionCode=3,新卡样式 "xxx" 已保存
        val saveResultText = context.getString(com.soundrecorder.common.R.string.string_with_quote, fileName.title() ?: "")
        val fileNameText = context.getString(com.soundrecorder.common.R.string.recorder_saved_new)
        return Pair(fileNameText, saveResultText)
    }

    /**
     * 获取最新一个波形数据
     */
    @JvmStatic
    fun getLastOneAmp(): Int {
        return if (recorderViewModelApi?.isAlreadyRecording() == true) {
            recorderViewModelApi?.getAmplitudeList()?.lastOrNull() ?: 0
        } else {
            0
        }
    }

    /**
     * 获取波形list、标记list
     */
    @JvmStatic
    fun getLastAmpListAndMarkList(): Triple<Int, List<Int>, List<MarkDataBean>> {
        val ampList = recorderViewModelApi?.getAmplitudeList() ?: listOf()
        val totalSize = ampList.size
        val targetAmp = ampList.run {
            if (size <= WAVE_MAX_SIZE) {
                this
            } else {
                subList(size - WAVE_MAX_SIZE, size)
            }.reversed()
        }
        val targetMarks = mutableListOf<MarkDataBean>()
        recorderViewModelApi?.getMarkData<MarkDataBean>()?.run {
            if (size <= MARK_MAX_SIZE) {
                this
            } else {
                subList(size - MARK_MAX_SIZE, size)
            }
        }?.forEach {
            targetMarks.add(MarkDataBean(it.timeInMills, it.version).apply {
                it.correctTime = it.correctTime
                it.defaultNo = it.defaultNo
                it.isDefault = it.isDefault
                it.markText = it.markText
            })
        }
        return Triple(totalSize, targetAmp, targetMarks)
    }

    /**
     * @param originSaveFileState 保存状态的原始值
     */
    @JvmStatic
    private fun isSavingState(originSaveFileState: Int): Boolean =
        originSaveFileState != com.soundrecorder.modulerouter.recorder.SaveFileState.INIT

    /**
     * 获取保存进度（预估值）
     */
    @JvmStatic
    fun getSaveProgressValue(): Int {
        return recorderViewModelApi?.getSaveProgressValue() ?: 0
    }
}