/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: SmallCardDataUtil
 * Description:
 * Version: 1.0
 * Date: 2023/10/31
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2023/10/31 1.0 create
 */

package oplus.multimedia.soundrecorder.card.small

import android.content.Context
import com.soundrecorder.common.databean.markdata.MarkDataBean
import com.soundrecorder.modulerouter.recorder.PAUSED
import com.soundrecorder.modulerouter.recorder.RECORDING
import com.soundrecorder.modulerouter.recorder.RecorderServiceInterface
import com.soundrecorder.modulerouter.utils.Injector
import oplus.multimedia.soundrecorder.card.CardUtils
import oplus.multimedia.soundrecorder.card.CardUtils.SMALL_CARD_VERSION_CODE_3

object SmallCardDataUtil {

    @JvmStatic
    var smallCardVersionCode: Int = CardUtils.getSmallCardVersionCode()
    private const val RECORDER_CARD_DATA_VERSION_CODE_3 = 3

    private val recorderViewModelApi by lazy {
        Injector.injectFactory<RecorderServiceInterface>()
    }

    @JvmStatic
    fun getVersionCode(): Int {
        return RECORDER_CARD_DATA_VERSION_CODE_3
    }

    /**
     * 录制状态值
     * @param originSaveState 保存状态的原始值
     * @return first: 录制状态真实值(转换成卡片数据的录制状态值) second:录制按钮显示状态值
     */
    @JvmStatic
    fun getRecordStateValue(originSaveState: Int): Pair<Int, Int> {
        val recordState = recorderViewModelApi?.getCurrentStatus() ?: com.soundrecorder.modulerouter.recorder.SaveFileState.INIT
        val recordButtonShowState = if (isSavingState(originSaveState)) {
            recorderViewModelApi?.getRecordStatusBeforeSaving()
                ?: recordState
        } else {
            recordState
        }
        return Pair(
            convertRecordStateValue(recordState),
            convertRecordStateValue(recordButtonShowState)
        )
    }

    @JvmStatic
    private fun convertRecordStateValue(originRecordState: Int): Int {
        return when (originRecordState) {
            RECORDING -> RecorderState.RECORDING
            PAUSED -> RecorderState.PAUSED
            else -> RecorderState.INIT
        }
    }

    /**
     * 获取录音保存状态
     * @param showSuccessView 卡片是否显示成功VIEW
     * @param originSaveState 保存状态的原始值
     * @return 保存状态值（转换成卡片数据的保存状态值）
     */
    @JvmStatic
    fun getSaveStateValue(showSuccessView: Boolean, originSaveState: Int): Int {
        if (showSuccessView) {
            return SaveFileState.SUCCESS
        }
        return when (originSaveState) {
            com.soundrecorder.modulerouter.recorder.SaveFileState.INIT -> SaveFileState.INIT
            com.soundrecorder.modulerouter.recorder.SaveFileState.START_LOADING -> SaveFileState.START_LOADING
            com.soundrecorder.modulerouter.recorder.SaveFileState.SHOW_LOADING_DIALOG -> SaveFileState.SHOW_DIALOG
            com.soundrecorder.modulerouter.recorder.SaveFileState.SUCCESS -> SaveFileState.SUCCESS
            com.soundrecorder.modulerouter.recorder.SaveFileState.ERROR -> SaveFileState.ERROR
            else -> SaveFileState.INIT
        }
    }

    /**
     * 卡片录制时长TEXT、talkback描述、textColor
     * @param time 录制时长
     * @param recordStateValue 录制状态-转换成卡片数据的状态值
     * @return fist：录制时长TEXT
     * @return second：录制时长TEXT的talkback描述
     * @return third：录制时长TEXT的textColor
     */
    @JvmStatic
    fun getRecordTimeTextAndTalkDec(
        context: Context,
        time: Long,
        recordStateValue: Int,
    ): Triple<String, String, Int> {
        return if (isSmallVersion3OrLater()) {
            SmallCardDataVersionCode3Impl.getRecordTimeTextAndTalkDec(
                context,
                time,
                recordStateValue
            )
        } else {
            SmallCardDataVersionCode2Impl.getRecordTimeTextAndTalkDec(
                context,
                time,
                recordStateValue
            )
        }
    }

    /**
     * 录制按钮相关信息（enable状态、资源ID）、（背景ID、talkback描述）
     * @param originSaveState 保存状态的原始值
     * @param recordStateValue 转成卡片数据中的录制状态值
     * @return
     */
    @JvmStatic
    fun getRecordButtonData(
        context: Context,
        recordStateValue: Int,
        originSaveState: Int,
        pausedAudioChange: Boolean
    ): Pair<Int, String> {
        return if (isSmallVersion3OrLater()) {
            SmallCardDataVersionCode3Impl.getRecordButtonData(
                context,
                recordStateValue,
                originSaveState,
                pausedAudioChange
            )
        } else {
            SmallCardDataVersionCode2Impl.getRecordButtonData(
                context,
                recordStateValue,
                originSaveState,
                pausedAudioChange
            )
        }
    }


    /**
     * 标记按钮enable状态、资源ID、背景资源ID
     * @return first:enable状态
     *  second：显示资源id
     *  third：背景资源id
     */
    @JvmStatic
    fun getMarkButtonData(): Triple<Boolean, Int, Int> {
        return if (isSmallVersion3OrLater()) {
            SmallCardDataVersionCode3Impl.getMarkButtonData()
        } else {
            SmallCardDataVersionCode2Impl.getMarkButtonData()
        }
    }

    /**
     * 保存按钮enable状态、资源ID、背景资源ID
     * @param originSaveState 保存状态的原始值
     * @return first:enable状态
     *  second：显示资源id
     *  third：背景资源id
     */
    @JvmStatic
    fun getSaveButtonData(originSaveState: Int): Triple<Boolean, Int, Int> {
        return if (isSmallVersion3OrLater()) {
            SmallCardDataVersionCode3Impl.getSaveButtonData(originSaveState)
        } else {
            SmallCardDataVersionCode2Impl.getSaveButtonData(originSaveState)
        }
    }

    /**
     * 卡片保存中loadingView的文本
     * @param originSaveState 保存状态的原始值
     */
    @JvmStatic
    fun getSaveLoadingText(context: Context, originSaveState: Int): String {
        if (isSavingState(originSaveState)) {
            return context.getString(com.soundrecorder.common.R.string.is_saving)
        }
        return ""
    }

    /**
     * 卡片保存成功View的文本内容
     * @param context
     * @param saveStateValue 保存状态，转换成SmallCardData.SaveFileState后的值
     * @param fileName  保存音频名称，含后缀
     */
    @JvmStatic
    fun getSaveSuccessViewData(
        context: Context,
        saveStateValue: Int,
        fileName: String
    ): Pair<String, String> {
        return if (isSmallVersion3OrLater()) {
            SmallCardDataVersionCode3Impl.getSaveSuccessViewData(context, saveStateValue, fileName)
        } else {
            SmallCardDataVersionCode2Impl.getSaveSuccessViewData(context, saveStateValue, fileName)
        }
    }

    /**
     * 获取最新一个波形数据
     */
    @JvmStatic
    fun getLastOneAmp(): Int {
        return if (isSmallVersion3OrLater()) {
            SmallCardDataVersionCode3Impl.getLastOneAmp()
        } else {
            SmallCardDataVersionCode2Impl.getLastOneAmp()
        }
    }

    /**
     * 获取波形list、标记list
     */
    @JvmStatic
    fun getLastAmpListAndMarkList(): Triple<Int, List<Int>, List<MarkDataBean>> {
        return if (isSmallVersion3OrLater()) {
            SmallCardDataVersionCode3Impl.getLastAmpListAndMarkList()
        } else {
            SmallCardDataVersionCode2Impl.getLastAmpListAndMarkList()
        }
    }

    @JvmStatic
    fun isSmallVersion3OrLater(): Boolean = smallCardVersionCode >= SMALL_CARD_VERSION_CODE_3

    @JvmStatic
    fun isSavingState(originSaveFileState: Int): Boolean =
        originSaveFileState != com.soundrecorder.modulerouter.recorder.SaveFileState.INIT

    /**
     * 获取保存进度值（预估值）。
     */
    @JvmStatic
    fun getProgressValue(): Int {
        return if (isSmallVersion3OrLater()) {
            SmallCardDataVersionCode3Impl.getSaveProgressValue()
        } else {
            SmallCardDataVersionCode2Impl.getSaveProgressValue()
        }
    }
}