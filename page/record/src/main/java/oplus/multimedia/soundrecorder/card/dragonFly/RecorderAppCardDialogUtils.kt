/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: OtherDisplayRecordSaveDialogUtils
 Description:
 Version: 1.0
 Date: 2022/8/9
 Author: W9013333(v-zhen<PERSON><PERSON><PERSON><PERSON>@oppo.com)
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9013333 2022/8/9 1.0 create
 */
package oplus.multimedia.soundrecorder.card.dragonFly

import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.view.View
import android.view.WindowManager
import android.widget.FrameLayout
import android.widget.TextView
import androidx.annotation.MainThread
import androidx.core.view.doOnPreDraw
import androidx.core.view.updateLayoutParams
import com.coui.appcompat.panel.COUIBottomSheetDialog
import com.soundrecorder.base.ext.title
import com.soundrecorder.base.utils.ClickUtils
import com.soundrecorder.common.utils.AppCardUtils.addContinueFlag
import com.soundrecorder.modulerouter.BrowseFileInterface
import com.soundrecorder.modulerouter.recorder.RecorderDataConstant
import com.soundrecorder.modulerouter.utils.Injector
import com.soundrecorder.record.R

object RecorderAppCardDialogUtils {

    private val browseFileApi by lazy {
        Injector.injectFactory<BrowseFileInterface>()
    }

    @JvmStatic
    @MainThread
    @Suppress("TooGenericExceptionCaught")
    fun Context.showSaveFileSuccessDialog(fileName: String, btnViewAction: () -> Unit): COUIBottomSheetDialog? {
        return showOtherDisplayDialog {
            val contentView = it.layoutInflater.inflate(R.layout.dragon_fly_card_save_file_success_layout, null)
            contentView.findViewById<TextView>(R.id.tvRecordTitle).text = fileName.title()
            val appIcon = try {
                it.context.packageManager.getApplicationIcon(it.context.applicationInfo).apply {
                    val w = resources.getDimensionPixelSize(com.soundrecorder.common.R.dimen.dp32)
                    setBounds(0, 0, w, w)
                }
            } catch (e: Exception) {
                null
            }
            contentView.findViewById<TextView>(R.id.tvAppInfo).setCompoundDrawablesRelative(null, appIcon, null, null)
            contentView.findViewById<View>(R.id.btnView).setOnClickListener { _ ->
                if (!ClickUtils.isFastDoubleClick()) {
                    btnViewAction.invoke()
                    it.dismiss(false)
                    continueMainTaskWhenPreview(fileName)
                }
            }
            contentView
        }
    }

    @JvmStatic
    @Suppress("TooGenericExceptionCaught")
    private fun Context.showOtherDisplayDialog(v: (COUIBottomSheetDialog) -> View): COUIBottomSheetDialog? {
        return try {
            COUIBottomSheetDialog(this, com.support.panel.R.style.DefaultBottomSheetDialog).apply {
                window?.decorView?.isForceDarkAllowed = false
                contentView = v.invoke(this)
                findViewById<View>(com.google.android.material.R.id.coordinator)?.doOnPreDraw {
                    setPanelBackgroundTintColor(Color.BLACK)
                    it.updateLayoutParams<FrameLayout.LayoutParams> {
                        setMargins(0, resources.getDimensionPixelOffset(com.soundrecorder.common.R.dimen.dp32), 0, 0)
                        setHeight(maxHeight())
                    }
                }
                show()
            }
        } catch (e: Exception) {
            null
        }
    }

    @JvmStatic
    private fun Context.maxHeight(): Int {
        val windowManager = getSystemService(WindowManager::class.java)
        return windowManager.currentWindowMetrics.bounds.height()
    }

    /**
     * 展开续接录音主页查看
     */
    @JvmStatic
    private fun Context.continueMainTaskWhenPreview(fileName: String) {
        startActivity(Intent(this, browseFileApi?.getBrowseFileClass()).apply {
            addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
            putExtra(RecorderDataConstant.SHOULD_AUTO_FIND_FILE_NAME, fileName)
            addContinueFlag()
        })
    }
}