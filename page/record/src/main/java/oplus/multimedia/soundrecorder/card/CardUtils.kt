/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: CardUtils
 Description:
 Version: 1.0
 Date: 2023/2/13
 Author: W9013333(v-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@oppo.com)
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9013333 2023/2/13 1.0 create
 */

package oplus.multimedia.soundrecorder.card

import android.net.Uri
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.AppUtil

object CardUtils {
    /**
     * 桌面卡片升级(#6116369)改变了卡片样式，波形修改同内屏一致
     */
    const val SMALL_CARD_VERSION_CODE_3 = 3
    /*修改卡片标记保存按钮展开闪烁问题*/
    const val SMALL_CARD_VERSION_CODE_4 = 4
    //卡片精品化，添加进度显示的起始版本。
    const val SMALL_CARD_VERSION_CODE_5 = 5

    private const val SMART_ENGINE_PACKAGE = "com.oplus.smartengine"
    private const val META_DATA_SMALL_CARD_VERSION_CODE = "com.soundRecorder.smallCard.versionCode"
    private const val META_DATA_DRAGON_FLY_CARD_VERSION_CODE = "com.soundRecorder.dragonFlyCard.versionCode"
    private val REFRESH_DRAGON_FLY_DATA_URI = Uri.parse("content://com.soundrecorder.dragonfly.AppCardWidgetProvider")
    private val REFRESH_SMALL_DATA_URI = Uri.parse("content://com.soundrecorder.common.provider.recorder.smallcard")
    private const val VERSION_CODE_2 = 2

    @JvmStatic
    fun isSmallVersionCode3OrLater(): Boolean {
        return getSmallCardVersionCode() >= SMALL_CARD_VERSION_CODE_3
    }

    @JvmStatic
    fun isSmallVersionCode2OrLater(): Boolean {
        return getSmallCardVersionCode() >= VERSION_CODE_2
    }

    @JvmStatic
    fun getSmallCardVersionCode(): Int {
        return AppUtil.metaDataInt(SMART_ENGINE_PACKAGE, META_DATA_SMALL_CARD_VERSION_CODE)
    }

    @JvmStatic
    fun isDragonFlyVersionCode2OrLater(): Boolean {
        val vc = AppUtil.metaDataInt(SMART_ENGINE_PACKAGE, META_DATA_DRAGON_FLY_CARD_VERSION_CODE)
        return vc >= VERSION_CODE_2
    }

    /**
     * 录音新的负一屏、桌面录制卡片刷新方式
     */
    @JvmStatic
    fun refreshSmallCardData() {
        BaseApplication.getAppContext().contentResolver.notifyChange(REFRESH_SMALL_DATA_URI, null)
    }


    /**
     * 录音新蜻蜓等设备副屏卡片刷新方式
     */
    @JvmStatic
    fun refreshDragonFlyCardData() {
        BaseApplication.getAppContext().contentResolver.notifyChange(REFRESH_DRAGON_FLY_DATA_URI, null)
    }
}