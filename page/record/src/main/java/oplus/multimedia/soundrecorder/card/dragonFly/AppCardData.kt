/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: AppCardData
 Description:
 Version: 1.0
 Date: 2022/8/29
 Author: W9013333(<EMAIL>)
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9013333 2022/8/29 1.0 create
 */

package oplus.multimedia.soundrecorder.card.dragonFly

import android.graphics.Color
import androidx.annotation.ColorInt
import androidx.annotation.DrawableRes
import androidx.annotation.Keep

@Keep
data class AppCardData(
    val packageName: String,
    val widgetCode: String,
    val recordState: Int,
    val saveFileState: Int,
    val timeText: String,
    @ColorInt val timeTextColor: Int,
    val isFakeBoldText: Boolean = true,
    val timeDes: String,
    val stateText: String,
    val recorderName: String,
    @ColorInt val stateTextColor: Int,
    val fileName: String,
    var markSrc: Int,
    @DrawableRes val saveFileSrc: Int,
    @DrawableRes val recordStateSrc: Int,
    @DrawableRes val markRippleSrc: Int,
    @DrawableRes val stateRippleSrc: Int,
    @DrawableRes val saveRippleSrc: Int,
    @DrawableRes val ampsSize: Int,
    val lastAmps: List<Int>,
    @ColorInt val cardColor: Int = Color.parseColor("#FFFAFAFA"),
    @ColorInt var cardWaveColor: Int = Color.parseColor("#D9000000"),
    @ColorInt var cardDashWaveColor: Int = Color.parseColor("#D9666666"),
    val markEnabled: Boolean = true,
    val switchEnabled: Boolean = true,
    val saveEnabled: Boolean = true,
    /**
     * 蜻蜓副屏波形是否支持70毫秒刷新
     */
    val hasRefreshTime70: Boolean = true,
    val markDesc: String = "",
    val recordStateDesc: String = "",
    val saveDesc: String = "",
)

object RecorderState {
    const val INIT = 0
    const val RECORDING = 1
    const val PAUSED = 2
}

object SaveFileState {
    const val INIT = 0
    const val START_LOADING = 1
    const val SHOW_DIALOG = 2
    const val SUCCESS = 3
    const val ERROR = 4
}
