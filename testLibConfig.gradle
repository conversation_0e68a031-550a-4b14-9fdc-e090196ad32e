/*******************************************************************************
 * * Copyright (C), 2010-2020, Oplus Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  testLibConfig.gradle
 * * Description: jacoco
 * * Version: 1.0
 * * Date : 21-4-8 下午12:02
 * * Author: 80237994
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>      <data>       <version >    <desc>
 * *  80237994    21-4-8 下午12:02      1.0    build this module
 ******************************************************************************/

def isNeedJacocoExecute = true  // Jacoco插件开关
def isEclipseProject = false  // 是否是Eclipse项目结构
/******************************begin==单元测试依赖包==begin***********************************************/

ext.deps = [:]
def versions = [:]

//kotlin
versions.kotlin = libs.versions.kotlin
versions.kotlin_coroutines = libs.versions.coroutines
versions.kotlin_core = '1.4.0'
//androidx support
versions.support = '1.2.0'

versions.arch = "2.2.0"

//test
versions.junit = "4.13.2"
versions.jacoco = "0.8.7"
versions.junit5 = "5.8.1"
versions.uiautomator = "2.2.0"
versions.mockito = "3.7.7"
versions.runner = "1.5.2"
versions.rules = "1.5.0"
versions.espresso = "3.1.0"
versions.powermock = "2.0.2"
versions.robolectric = "4.7.1"
versions.mockito_android = "2.28.2"
versions.androidXTestExtKotlinRunner = '1.1.5'
versions.ext_truth= "1.5.0"
versions.hamcrest = '1.3'
versions.truth = '0.44'
versions.otestPlatform = '1.0.7@aar'
versions.mockwebserver = "4.7.2"
versions.mockk = "1.12.2"

deps.androidTestLib = [
        // Dependencies for Android unit tests
        "junit:junit:$versions.junit",
        "androidx.test.uiautomator:uiautomator:$versions.uiautomator",
//        "org.mockito:mockito-core:$versions.mockito",
        // AndroidX Test - Instrumented testing
        "androidx.test:runner:$versions.runner",
        "androidx.test:core-ktx:$versions.kotlin_core",
        "androidx.test.ext:junit:$versions.androidXTestExtKotlinRunner",
        "androidx.test.ext:junit-ktx:$versions.androidXTestExtKotlinRunner",
        "androidx.test.ext:truth:$versions.ext_truth",
        "androidx.test:rules:$versions.rules",
        "androidx.arch.core:core-testing:$versions.arch",
        "androidx.test.espresso:espresso-core:$versions.espresso",
        "androidx.test.espresso:espresso-web:$versions.espresso",
        "androidx.test.espresso:espresso-contrib:$versions.espresso",
        "androidx.test.espresso:espresso-intents:$versions.espresso",
        "androidx.test.espresso:espresso-accessibility:$versions.espresso",
        "androidx.test.espresso.idling:idling-concurrent:$versions.espresso",
        "androidx.test.espresso:espresso-idling-resource:$versions.espresso",
        "org.robolectric:annotations:$versions.robolectric",
        "org.mockito:mockito-android:$versions.mockito_android",
        //oppo test
        "otestPlatform:testLib:$versions.otestPlatform"
]

deps.testLib = [
        // Dependencies for local unit tests
        "junit:junit:$versions.junit",
        "org.mockito:mockito-inline:$versions.mockito",
        "org.hamcrest:hamcrest-all:$versions.hamcrest",
        "androidx.arch.core:core-testing:$versions.arch",
        "org.jetbrains.kotlinx:kotlinx-coroutines-android:$versions.kotlin_coroutines",
        "org.jetbrains.kotlinx:kotlinx-coroutines-test:$versions.kotlin_coroutines",
        "org.robolectric:robolectric:$versions.robolectric",
        "androidx.test.espresso:espresso-core:$versions.espresso",
        "androidx.test.espresso:espresso-contrib:$versions.espresso",
        "androidx.test.espresso:espresso-intents:$versions.espresso",
        "com.google.truth:truth:$versions.truth",
        "org.powermock:powermock-api-mockito2:$versions.powermock",
        "org.powermock:powermock-module-junit4:$versions.powermock",
        "org.powermock:powermock-core:$versions.powermock",
        "org.powermock:powermock-module-junit4-rule:$versions.powermock",
        "org.powermock:powermock-classloading-xstream:$versions.powermock",
        "com.squareup.okhttp3:mockwebserver:$versions.mockwebserver",
        "io.mockk:mockk:${versions.mockk}",

        // AndroidX Test - JVM testing
        "androidx.test:runner:$versions.runner",
        "androidx.test:core:$versions.kotlin_core",
        "androidx.test:core-ktx:$versions.kotlin_core",
        "androidx.test.ext:junit:$versions.androidXTestExtKotlinRunner",
        "androidx.test.ext:junit-ktx:$versions.androidXTestExtKotlinRunner",
        "androidx.test.ext:truth:$versions.ext_truth",
        "androidx.test:rules:$versions.rules"
]

deps.commonLib = [
        "com.sectools.check:olint:+" //自动加载最新版本,可以获取最新扫描规则
]

deps.runtimeTestLib = [
        "org.junit.jupiter:junit-jupiter-engine:$versions.junit5",
        "org.junit.vintage:junit-vintage-engine:$versions.junit5"
]
/******************************end==单元测试依赖包==end***********************************************/

// 如果主module是 eclipse结构，subprojects替换为allprojects
def allProjects = isEclipseProject ? project.rootProject.allprojects : project.rootProject.subprojects

configure(allProjects) { prj ->
    apply plugin: 'jacoco'
    jacoco {
        toolVersion = versions.jacoco
    }

    prj.afterEvaluate {
        if (project.properties.containsKey('android')) { //针对android应用和android库项目
            //Test dependencies
            for (def lib : deps.androidTestLib) {
                prj.dependencies.add("androidTestImplementation", lib)
            }
            for (def lib : deps.testLib) {
                prj.dependencies.add("testImplementation", lib)
            }

            for (def lib : deps.commonLib) {
                prj.dependencies.add("implementation", lib)
            }

            //robolectric configure
            project.properties['android'].testOptions {
                unitTests {
                    includeAndroidResources = true
                    returnDefaultValues = true
                }

                unitTests.all {
                    jacoco {
                        includeNoLocationClasses = true
                        excludes = ['jdk.internal.*']
                    }
                    jvmArgs '-noverify'
                    maxHeapSize = "10240m"
                    forkEvery = 10
                    if (!org.gradle.internal.os.OperatingSystem.current().isWindows()) {
                        systemProperty 'robolectric.offline', 'true'
                        systemProperty 'robolectric.dependency.dir', "${project.rootProject.rootDir}/robolectric/12-robolectric-7732740-i3"
                    }
                    reports.html.destination = file("${project.buildDir}/UnitTestReport")
                }
            }
        }
    }
}
